package com.lanshan.base.commonservice.excel;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.annotation.ExcelProperty;
import com.lanshan.base.commonservice.common.excel.handler.HeaderWidthContentHeightHandler;
import org.junit.Test;

import java.io.FileOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * 表头宽度自适应 + 内容高度自适应功能测试
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/7/6
 */
public class HeaderWidthContentHeightTest {

    /**
     * 测试数据DTO
     */
    public static class TestDataDTO {
        @ExcelProperty("姓名")
        private String name;

        @ExcelProperty("学号")
        private String studentId;

        @ExcelProperty("详细地址信息")
        private String address;

        @ExcelProperty("个人简介和特长描述")
        private String description;

        @ExcelProperty("备注")
        private String remark;

        // 构造函数
        public TestDataDTO(String name, String studentId, String address, String description, String remark) {
            this.name = name;
            this.studentId = studentId;
            this.address = address;
            this.description = description;
            this.remark = remark;
        }

        // Getters and Setters
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        public String getStudentId() { return studentId; }
        public void setStudentId(String studentId) { this.studentId = studentId; }
        public String getAddress() { return address; }
        public void setAddress(String address) { this.address = address; }
        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }
        public String getRemark() { return remark; }
        public void setRemark(String remark) { this.remark = remark; }
    }

    /**
     * 创建测试数据
     */
    private List<TestDataDTO> createTestData() {
        List<TestDataDTO> testData = new ArrayList<>();
        
        testData.add(new TestDataDTO(
                "张三",
                "2023001",
                "北京市海淀区中关村大街1号",
                "计算机科学与技术专业学生，擅长Java编程",
                "优秀学生"
        ));
        
        testData.add(new TestDataDTO(
                "李四",
                "2023002",
                "上海市浦东新区陆家嘴金融贸易区世纪大道88号金茂大厦",
                "软件工程专业学生，熟悉前端开发技术，包括HTML、CSS、JavaScript、Vue.js、React等技术栈，曾参与多个项目开发",
                "积极参与社团活动，担任学生会干部"
        ));
        
        testData.add(new TestDataDTO(
                "王五",
                "2023003",
                "广州市天河区珠江新城花城大道85号",
                "数据科学与大数据技术专业学生",
                "无"
        ));
        
        testData.add(new TestDataDTO(
                "赵六",
                "2023004",
                "深圳市南山区科技园南区深南大道9988号",
                "人工智能专业学生，研究方向为机器学习和深度学习，熟悉Python、TensorFlow、PyTorch等工具和框架，曾在国际会议上发表论文，获得多项奖学金",
                "科研能力突出，多次获得奖学金，参与国家级科研项目"
        ));
        
        testData.add(new TestDataDTO(
                "钱七",
                "2023005",
                "杭州市西湖区文三路259号昌地火炬大厦",
                "网络安全专业学生，专注于网络攻防技术研究，熟悉各种安全工具和渗透测试技术，具有丰富的实战经验，曾参与多个企业的安全评估项目，获得多个安全认证证书，在CTF竞赛中多次获奖",
                "网络安全技术专家，多次在CTF竞赛中获奖，具有丰富的实战经验和理论基础"
        ));

        return testData;
    }

    /**
     * 测试默认配置
     */
    @Test
    public void testDefaultConfig() throws IOException {
        List<TestDataDTO> testData = createTestData();

        try (FileOutputStream outputStream = new FileOutputStream("test_header_width_content_height_default.xlsx")) {
            EasyExcel.write(outputStream, TestDataDTO.class)
                    .registerWriteHandler(new HeaderWidthContentHeightHandler())
                    .sheet("默认配置测试")
                    .doWrite(testData);
        }

        System.out.println("默认配置测试完成，文件：test_header_width_content_height_default.xlsx");
        System.out.println("预期效果：");
        System.out.println("- 表头宽度根据表头文字长度自适应");
        System.out.println("- 长内容通过增加行高显示，自动换行");
        System.out.println("- 列宽保持相对固定，不会因为内容过长而变得很宽");
    }

    /**
     * 测试自定义表头宽度配置
     */
    @Test
    public void testCustomHeaderWidth() throws IOException {
        List<TestDataDTO> testData = createTestData();

        try (FileOutputStream outputStream = new FileOutputStream("test_header_width_content_height_custom_header.xlsx")) {
            EasyExcel.write(outputStream, TestDataDTO.class)
                    .registerWriteHandler(HeaderWidthContentHeightHandler.createWithHeaderConfig(25, 12))
                    .sheet("自定义表头宽度测试")
                    .doWrite(testData);
        }

        System.out.println("自定义表头宽度测试完成，文件：test_header_width_content_height_custom_header.xlsx");
        System.out.println("配置：最大表头宽度25字符，最小表头宽度12字符");
    }

    /**
     * 测试自定义行高配置
     */
    @Test
    public void testCustomRowHeight() throws IOException {
        List<TestDataDTO> testData = createTestData();

        try (FileOutputStream outputStream = new FileOutputStream("test_header_width_content_height_custom_row.xlsx")) {
            EasyExcel.write(outputStream, TestDataDTO.class)
                    .registerWriteHandler(HeaderWidthContentHeightHandler.createWithRowHeightConfig(200f, 18f))
                    .sheet("自定义行高测试")
                    .doWrite(testData);
        }

        System.out.println("自定义行高测试完成，文件：test_header_width_content_height_custom_row.xlsx");
        System.out.println("配置：最大行高200磅，最小行高18磅");
    }

    /**
     * 测试完全自定义配置
     */
    @Test
    public void testFullCustomConfig() throws IOException {
        List<TestDataDTO> testData = createTestData();

        HeaderWidthContentHeightHandler handler = new HeaderWidthContentHeightHandler(
                35,    // 表头最大宽度
                10,    // 表头最小宽度
                3,     // 表头额外宽度
                180f,  // 数据行最大高度
                15f,   // 数据行最小高度
                30f,   // 表头行高
                18f,   // 默认行高
                18f,   // 每行文字高度
                20,    // 每行字符数阈值
                true   // 启用中文字符优化
        );

        try (FileOutputStream outputStream = new FileOutputStream("test_header_width_content_height_full_custom.xlsx")) {
            EasyExcel.write(outputStream, TestDataDTO.class)
                    .registerWriteHandler(handler)
                    .sheet("完全自定义配置测试")
                    .doWrite(testData);
        }

        System.out.println("完全自定义配置测试完成，文件：test_header_width_content_height_full_custom.xlsx");
        System.out.println("配置：表头最大宽度35字符，每行字符数阈值20字符，最大行高180磅");
    }

    /**
     * 对比测试：与传统AutoSizeHandler的区别
     */
    @Test
    public void testComparison() throws IOException {
        List<TestDataDTO> testData = createTestData();

        // 使用HeaderWidthContentHeightHandler
        try (FileOutputStream outputStream = new FileOutputStream("comparison_header_width_content_height.xlsx")) {
            EasyExcel.write(outputStream, TestDataDTO.class)
                    .registerWriteHandler(new HeaderWidthContentHeightHandler())
                    .sheet("表头宽度+内容高度自适应")
                    .doWrite(testData);
        }

        System.out.println("对比测试完成：");
        System.out.println("- comparison_header_width_content_height.xlsx：表头宽度自适应+内容高度自适应");
        System.out.println();
        System.out.println("区别说明：");
        System.out.println("- HeaderWidthContentHeightHandler：列宽固定（根据表头），长内容通过行高显示");
        System.out.println("- 传统AutoSizeHandler：列宽会根据最长内容调整，可能导致某些列过宽");
    }
}
