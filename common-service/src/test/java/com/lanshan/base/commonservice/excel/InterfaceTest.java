package com.lanshan.base.commonservice.excel;

import com.alibaba.excel.write.handler.RowWriteHandler;
import com.alibaba.excel.write.handler.WorkbookWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteWorkbookHolder;
import org.apache.poi.ss.usermodel.Row;

/**
 * 测试EasyExcel接口的正确方法签名
 */
public class InterfaceTest {

    public static class TestRowWriteHandler implements RowWriteHandler {
        // 这里会显示RowWriteHandler接口的正确方法
    }
    
    public static class TestWorkbookWriteHandler implements WorkbookWriteHandler {
        // 这里会显示WorkbookWriteHandler接口的正确方法
    }
}
