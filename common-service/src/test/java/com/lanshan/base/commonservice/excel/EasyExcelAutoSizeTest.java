package com.lanshan.base.commonservice.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.lanshan.base.commonservice.common.excel.util.EasyExcelAutoSizeUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.junit.jupiter.api.Test;

import java.io.FileOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * EasyExcel自适应大小功能测试
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/7/6
 */
public class EasyExcelAutoSizeTest {

    /**
     * 测试数据DTO
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TestDataDTO {

        @ExcelProperty("姓名")
        private String name;

        @ExcelProperty("年龄")
        private Integer age;

        @ExcelProperty("邮箱地址")
        private String email;

        @ExcelProperty("详细地址")
        private String address;

        @ExcelProperty("备注信息")
        private String remark;

        @ExcelProperty("长文本内容")
        private String longText;
    }

    /**
     * 测试基础自适应功能
     */
    @Test
    public void testBasicAutoSize() throws IOException {
        List<TestDataDTO> testData = createTestData();

        try (FileOutputStream outputStream = new FileOutputStream("test_basic_auto_size.xlsx")) {
            EasyExcelAutoSizeUtil.writeWithAutoSize(outputStream, "测试数据", TestDataDTO.class, testData);
        }

        System.out.println("基础自适应测试完成，文件：test_basic_auto_size.xlsx");
    }

    /**
     * 测试只有列宽自适应
     */
    @Test
    public void testColumnWidthOnly() throws IOException {
        List<TestDataDTO> testData = createTestData();

        try (FileOutputStream outputStream = new FileOutputStream("test_column_width_only.xlsx")) {
            EasyExcelAutoSizeUtil.writeWithAutoColumnWidth(outputStream, "测试数据", TestDataDTO.class, testData);
        }

        System.out.println("列宽自适应测试完成，文件：test_column_width_only.xlsx");
    }

    /**
     * 测试只有行高自适应
     */
    @Test
    public void testRowHeightOnly() throws IOException {
        List<TestDataDTO> testData = createTestData();

        try (FileOutputStream outputStream = new FileOutputStream("test_row_height_only.xlsx")) {
            EasyExcelAutoSizeUtil.writeWithAutoRowHeight(outputStream, "测试数据", TestDataDTO.class, testData);
        }

        System.out.println("行高自适应测试完成，文件：test_row_height_only.xlsx");
    }

    /**
     * 测试自定义配置
     */
    @Test
    public void testCustomConfig() throws IOException {
        List<TestDataDTO> testData = createTestData();

        try (FileOutputStream outputStream = new FileOutputStream("test_custom_config.xlsx")) {
            EasyExcelAutoSizeUtil.builder()
                    .maxColumnWidth(80)              // 最大列宽80字符
                    .minColumnWidth(12)              // 最小列宽12字符
                    .maxRowHeight(120f)              // 最大行高120磅
                    .minRowHeight(18f)               // 最小行高18磅
                    .headerRowHeight(30f)            // 标题行高30磅
                    .defaultRowHeight(20f)           // 默认行高20磅
                    .enableChineseOptimization(true) // 启用中文优化
                    .enableAutoWrap(true)            // 启用自动换行
                    .writeToStream(outputStream, "自定义配置测试", TestDataDTO.class, testData);
        }

        System.out.println("自定义配置测试完成，文件：test_custom_config.xlsx");
    }

    /**
     * 测试禁用中文优化
     */
    @Test
    public void testWithoutChineseOptimization() throws IOException {
        List<TestDataDTO> testData = createTestData();

        try (FileOutputStream outputStream = new FileOutputStream("test_without_chinese_optimization.xlsx")) {
            EasyExcelAutoSizeUtil.builder()
                    .enableChineseOptimization(false) // 禁用中文优化
                    .writeToStream(outputStream, "禁用中文优化测试", TestDataDTO.class, testData);
        }

        System.out.println("禁用中文优化测试完成，文件：test_without_chinese_optimization.xlsx");
    }

    /**
     * 测试禁用自动换行
     */
    @Test
    public void testWithoutAutoWrap() throws IOException {
        List<TestDataDTO> testData = createTestData();

        try (FileOutputStream outputStream = new FileOutputStream("test_without_auto_wrap.xlsx")) {
            EasyExcelAutoSizeUtil.builder()
                    .enableAutoWrap(false) // 禁用自动换行
                    .writeToStream(outputStream, "禁用自动换行测试", TestDataDTO.class, testData);
        }

        System.out.println("禁用自动换行测试完成，文件：test_without_auto_wrap.xlsx");
    }

    /**
     * 创建测试数据
     */
    private List<TestDataDTO> createTestData() {
        List<TestDataDTO> dataList = new ArrayList<>();

        // 添加各种长度的测试数据
        dataList.add(new TestDataDTO(
                "张三",
                25,
                "<EMAIL>",
                "北京市朝阳区某某街道123号",
                "这是一个简单的备注",
                "这是一段很长的文本内容，用来测试自适应行高功能。这段文本包含了多个句子，并且有足够的长度来触发换行。"
        ));

        dataList.add(new TestDataDTO(
                "李四",
                30,
                "<EMAIL>",
                "上海市浦东新区陆家嘴金融贸易区世纪大道88号金茂大厦",
                "备注信息可能会很长，包含多行内容\n第二行备注\n第三行备注",
                "短文本"
        ));

        dataList.add(new TestDataDTO(
                "王五",
                28,
                "<EMAIL>",
                "广州市天河区珠江新城",
                "简短备注",
                "这是另一段长文本，用于测试列宽自适应功能。文本中包含了中文和英文混合的内容，English and Chinese mixed content for testing purposes."
        ));

        dataList.add(new TestDataDTO(
                "赵六",
                35,
                "<EMAIL>",
                "深圳市南山区科技园南区",
                "多行备注内容：\n1. 第一项内容\n2. 第二项内容\n3. 第三项内容",
                "测试数据"
        ));

        dataList.add(new TestDataDTO(
                "钱七",
                22,
                "<EMAIL>",
                "杭州市西湖区文三路",
                "正常长度的备注信息",
                "这是一个包含特殊字符的文本：!@#$%^&*()_+-=[]{}|;':\",./<>?以及一些数字123456789"
        ));

        // 添加一些边界情况的测试数据
        dataList.add(new TestDataDTO(
                "测试用户名字很长很长很长",
                99,
                "<EMAIL>",
                "这是一个非常非常非常非常非常非常非常非常非常非常长的地址，用来测试列宽的最大限制功能",
                "",  // 空备注
                "单行"
        ));

        dataList.add(new TestDataDTO(
                "短",
                1,
                "a@b.c",
                "短址",
                "短",
                "这是一个用来测试最小列宽限制的数据行，大部分字段都很短，但是这个长文本字段很长很长很长很长很长"
        ));

        return dataList;
    }

    /**
     * 性能测试 - 大数据量
     */
    @Test
    public void testPerformanceWithLargeData() throws IOException {
        System.out.println("开始性能测试...");
        long startTime = System.currentTimeMillis();

        List<TestDataDTO> largeDataList = new ArrayList<>();
        for (int i = 0; i < 10000; i++) {
            largeDataList.add(new TestDataDTO(
                    "用户" + i,
                    20 + (i % 50),
                    "user" + i + "@test.com",
                    "地址" + i + "号",
                    "备注信息" + i,
                    "长文本内容" + i + "，这是一段用于性能测试的文本内容。"
            ));
        }

        try (FileOutputStream outputStream = new FileOutputStream("test_performance_large_data.xlsx")) {
            EasyExcelAutoSizeUtil.writeWithAutoSize(outputStream, "性能测试", TestDataDTO.class, largeDataList);
        }

        long endTime = System.currentTimeMillis();
        System.out.println("性能测试完成，耗时：" + (endTime - startTime) + "ms");
        System.out.println("数据量：" + largeDataList.size() + "行");
        System.out.println("文件：test_performance_large_data.xlsx");
    }
}
