package com.lanshan.base.commonservice.common.excel.handler;

import com.alibaba.excel.write.handler.RowWriteHandler;
import com.alibaba.excel.write.handler.WorkbookWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteTableHolder;
import com.alibaba.excel.write.metadata.holder.WriteWorkbookHolder;
import org.apache.poi.ss.usermodel.Row;

/**
 * EasyExcel自适应大小处理器（同时支持列宽和行高）
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/7/6
 */
public class AutoSizeHandler implements WorkbookWriteHandler, RowWriteHandler {

    /**
     * 列宽配置
     */
    private final ColumnWidthConfig columnWidthConfig;

    /**
     * 行高配置
     */
    private final RowHeightConfig rowHeightConfig;

    /**
     * 列宽处理器
     */
    private final AutoColumnWidthHandler columnWidthHandler;

    /**
     * 行高处理器
     */
    private final AutoRowHeightHandler rowHeightHandler;

    /**
     * 默认构造函数
     */
    public AutoSizeHandler() {
        this.columnWidthConfig = new ColumnWidthConfig();
        this.rowHeightConfig = new RowHeightConfig();
        this.columnWidthHandler = new AutoColumnWidthHandler(
                columnWidthConfig.isEnableChineseOptimization(),
                columnWidthConfig.getMaxColumnWidth(),
                columnWidthConfig.getMinColumnWidth()
        );
        this.rowHeightHandler = new AutoRowHeightHandler(
                rowHeightConfig.getMaxRowHeight(),
                rowHeightConfig.getMinRowHeight(),
                rowHeightConfig.getHeaderRowHeight(),
                rowHeightConfig.getDefaultRowHeight(),
                rowHeightConfig.getHeightPerLine(),
                rowHeightConfig.isEnableAutoWrap()
        );
    }

    /**
     * 自定义构造函数
     *
     * @param columnWidthConfig 列宽配置
     * @param rowHeightConfig   行高配置
     */
    public AutoSizeHandler(ColumnWidthConfig columnWidthConfig, RowHeightConfig rowHeightConfig) {
        this.columnWidthConfig = columnWidthConfig;
        this.rowHeightConfig = rowHeightConfig;
        this.columnWidthHandler = new AutoColumnWidthHandler(
                columnWidthConfig.isEnableChineseOptimization(),
                columnWidthConfig.getMaxColumnWidth(),
                columnWidthConfig.getMinColumnWidth()
        );
        this.rowHeightHandler = new AutoRowHeightHandler(
                rowHeightConfig.getMaxRowHeight(),
                rowHeightConfig.getMinRowHeight(),
                rowHeightConfig.getHeaderRowHeight(),
                rowHeightConfig.getDefaultRowHeight(),
                rowHeightConfig.getHeightPerLine(),
                rowHeightConfig.isEnableAutoWrap()
        );
    }

    // ==================== WorkbookWriteHandler 方法 ====================

    @Override
    public void afterWorkbookDispose(WriteWorkbookHolder writeWorkbookHolder) {
        // 委托给列宽处理器
        columnWidthHandler.afterWorkbookDispose(writeWorkbookHolder);
    }

    // ==================== RowWriteHandler 方法 ====================

    @Override
    public void afterRowCreate(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, Row row, Integer relativeRowIndex, Boolean isHead) {
        // 委托给行高处理器
        rowHeightHandler.afterRowCreate(writeSheetHolder, writeTableHolder, row, relativeRowIndex, isHead);
    }

    // ==================== 配置类 ====================

    /**
     * 列宽配置类
     */
    public static class ColumnWidthConfig {
        private int maxColumnWidth = 50;
        private int minColumnWidth = 8;
        private boolean enableChineseOptimization = true;

        public ColumnWidthConfig() {
        }

        public ColumnWidthConfig(int maxColumnWidth, int minColumnWidth, boolean enableChineseOptimization) {
            this.maxColumnWidth = maxColumnWidth;
            this.minColumnWidth = minColumnWidth;
            this.enableChineseOptimization = enableChineseOptimization;
        }

        // Getters and Setters
        public int getMaxColumnWidth() {
            return maxColumnWidth;
        }

        public void setMaxColumnWidth(int maxColumnWidth) {
            this.maxColumnWidth = maxColumnWidth;
        }

        public int getMinColumnWidth() {
            return minColumnWidth;
        }

        public void setMinColumnWidth(int minColumnWidth) {
            this.minColumnWidth = minColumnWidth;
        }

        public boolean isEnableChineseOptimization() {
            return enableChineseOptimization;
        }

        public void setEnableChineseOptimization(boolean enableChineseOptimization) {
            this.enableChineseOptimization = enableChineseOptimization;
        }
    }

    /**
     * 行高配置类
     */
    public static class RowHeightConfig {
        private float maxRowHeight = 100f;
        private float minRowHeight = 12f;
        private float headerRowHeight = 20f;
        private float defaultRowHeight = 15f;
        private float heightPerLine = 15f;
        private boolean enableAutoWrap = true;

        public RowHeightConfig() {
        }

        public RowHeightConfig(float maxRowHeight, float minRowHeight, float headerRowHeight,
                               float defaultRowHeight, float heightPerLine, boolean enableAutoWrap) {
            this.maxRowHeight = maxRowHeight;
            this.minRowHeight = minRowHeight;
            this.headerRowHeight = headerRowHeight;
            this.defaultRowHeight = defaultRowHeight;
            this.heightPerLine = heightPerLine;
            this.enableAutoWrap = enableAutoWrap;
        }

        // Getters and Setters
        public float getMaxRowHeight() {
            return maxRowHeight;
        }

        public void setMaxRowHeight(float maxRowHeight) {
            this.maxRowHeight = maxRowHeight;
        }

        public float getMinRowHeight() {
            return minRowHeight;
        }

        public void setMinRowHeight(float minRowHeight) {
            this.minRowHeight = minRowHeight;
        }

        public float getHeaderRowHeight() {
            return headerRowHeight;
        }

        public void setHeaderRowHeight(float headerRowHeight) {
            this.headerRowHeight = headerRowHeight;
        }

        public float getDefaultRowHeight() {
            return defaultRowHeight;
        }

        public void setDefaultRowHeight(float defaultRowHeight) {
            this.defaultRowHeight = defaultRowHeight;
        }

        public float getHeightPerLine() {
            return heightPerLine;
        }

        public void setHeightPerLine(float heightPerLine) {
            this.heightPerLine = heightPerLine;
        }

        public boolean isEnableAutoWrap() {
            return enableAutoWrap;
        }

        public void setEnableAutoWrap(boolean enableAutoWrap) {
            this.enableAutoWrap = enableAutoWrap;
        }
    }

    /**
     * 创建默认的自适应大小处理器
     *
     * @return AutoSizeHandler实例
     */
    public static AutoSizeHandler create() {
        return new AutoSizeHandler();
    }

    /**
     * 创建自定义配置的自适应大小处理器
     *
     * @param maxColumnWidth 最大列宽
     * @param minColumnWidth 最小列宽
     * @param maxRowHeight   最大行高
     * @param minRowHeight   最小行高
     * @return AutoSizeHandler实例
     */
    public static AutoSizeHandler create(int maxColumnWidth, int minColumnWidth, float maxRowHeight, float minRowHeight) {
        ColumnWidthConfig columnConfig = new ColumnWidthConfig(maxColumnWidth, minColumnWidth, true);
        RowHeightConfig rowConfig = new RowHeightConfig(maxRowHeight, minRowHeight, 20f, 15f, 15f, true);
        return new AutoSizeHandler(columnConfig, rowConfig);
    }
}
