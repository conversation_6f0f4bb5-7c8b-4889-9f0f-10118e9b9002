package com.lanshan.base.commonservice.typehandler;

import com.fasterxml.jackson.core.type.TypeReference;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
public class ObjectListTypeHandler extends AbsObjectJacksonTypeHandler<List<Object>> {
    @Override
    protected TypeReference<List<Object>> getTypeReference() {
        return new TypeReference<List<Object>>() {
        };
    }
}
