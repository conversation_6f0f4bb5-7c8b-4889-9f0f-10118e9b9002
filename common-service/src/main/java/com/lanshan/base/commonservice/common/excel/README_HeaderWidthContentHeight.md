# 智能列宽自适应 + 内容高度自适应功能

## 功能概述

`HeaderWidthContentHeightHandler` 是专门为您的需求设计的Excel处理器，实现了：

1. **智能列宽决策**：比较表头和内容宽度，取较长者作为列宽（不超过最大值）
2. **内容高度自适应**：超过列宽的内容通过增加行高来显示
3. **自动换行**：超过列宽的内容（表头或数据）都自动换行显示
4. **中文字符优化**：中文字符按2个字符宽度计算

## 列宽决策逻辑

- **如果内容比表头长且未达到最大值**：使用内容宽度作为列宽
- **如果表头比内容长且未达到最大值**：使用表头宽度作为列宽
- **如果都超过最大值**：使用最大值作为列宽，超出部分换行显示
- **如果都小于最小值**：使用最小值作为列宽

## 与传统方案的区别

### 传统 AutoSizeHandler
- ❌ 根据**所有内容**（表头+数据）调整列宽
- ❌ 长内容会导致列宽过宽，影响整体布局
- ❌ 可能出现某些列特别宽，其他列很窄的情况

### 新的 HeaderWidthContentHeightHandler
- ✅ 只根据**表头文字**调整列宽
- ✅ 长内容通过**增加行高**和**自动换行**显示
- ✅ 保持列宽相对固定，整体布局更美观
- ✅ 适合在有限页面宽度内显示更多列

## 使用方法

### 1. 基础用法（推荐）

```java
@PostMapping("/export")
public void export(@RequestBody QueryDTO qo, HttpServletResponse response) throws IOException {
    List<ExportDTO> data = service.exportData(qo);
    
    // 设置响应头
    response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
    response.setCharacterEncoding("utf-8");
    String fileName = URLEncoder.encode("导出文件", "UTF-8").replaceAll("\\+", "%20");
    response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");

    // 使用表头宽度自适应 + 内容高度自适应处理器
    EasyExcel.write(response.getOutputStream(), ExportDTO.class)
            .registerWriteHandler(new HeaderWidthContentHeightHandler())
            .sheet("数据表")
            .doWrite(data);
}
```

### 2. 自定义表头宽度

```java
// 自定义表头最大宽度35字符，最小宽度12字符
EasyExcel.write(response.getOutputStream(), ExportDTO.class)
        .registerWriteHandler(HeaderWidthContentHeightHandler.createWithHeaderConfig(35, 12))
        .sheet("数据表")
        .doWrite(data);
```

### 3. 自定义行高限制

```java
// 自定义最大行高200磅，最小行高18磅
EasyExcel.write(response.getOutputStream(), ExportDTO.class)
        .registerWriteHandler(HeaderWidthContentHeightHandler.createWithRowHeightConfig(200f, 18f))
        .sheet("数据表")
        .doWrite(data);
```

### 4. 完全自定义配置

```java
HeaderWidthContentHeightHandler handler = new HeaderWidthContentHeightHandler(
    40,    // 表头最大宽度（字符数）
    10,    // 表头最小宽度（字符数）
    3,     // 表头额外宽度（字符数）
    150f,  // 数据行最大高度（磅）
    15f,   // 数据行最小高度（磅）
    30f,   // 表头行高（磅）
    18f,   // 默认行高（磅）
    18f,   // 每行文字高度（磅）
    25,    // 每行字符数阈值
    true   // 启用中文字符优化
);

EasyExcel.write(response.getOutputStream(), ExportDTO.class)
        .registerWriteHandler(handler)
        .sheet("数据表")
        .doWrite(data);
```

## 配置参数说明

| 参数 | 默认值 | 说明 |
|------|--------|------|
| maxHeaderWidth | 30 | 表头最大宽度（字符数） |
| minHeaderWidth | 8 | 表头最小宽度（字符数） |
| headerExtraWidth | 2 | 表头额外宽度（美观留白） |
| maxRowHeight | 120f | 数据行最大高度（磅） |
| minRowHeight | 12f | 数据行最小高度（磅） |
| headerRowHeight | 25f | 表头行高（磅） |
| defaultRowHeight | 15f | 默认行高（磅） |
| heightPerLine | 16f | 每行文字高度（磅） |
| charsPerLine | 30 | 每行字符数阈值 |
| enableChineseOptimization | true | 启用中文字符优化 |

## 工作原理

### 表头宽度处理
1. 在表头行处理完成后，计算每个表头文字的显示宽度
2. 考虑中文字符占2个字符宽度
3. 加上额外宽度（美观留白）
4. 应用最大最小宽度限制
5. 设置列宽

### 内容高度处理
1. 在每个数据行创建时，计算行内容所需高度
2. 按换行符分割内容
3. 计算每行因长度产生的额外换行
4. 根据总行数计算所需高度
5. 应用最大最小高度限制
6. 设置行高

### 自动换行设置
1. 在工作簿处理完成后，为所有数据单元格设置自动换行样式
2. 设置垂直对齐方式为顶部对齐
3. 确保长内容能够正确换行显示

## 适用场景

✅ **推荐使用的场景：**
- 表头文字长度适中，但数据内容可能很长
- 希望保持表格列宽一致，避免某些列过宽
- 需要在有限的页面宽度内显示更多列
- 内容包含长文本、地址、描述等信息

❌ **不推荐使用的场景：**
- 所有内容都很短，希望列宽尽可能紧凑
- 需要根据数据内容动态调整列宽
- 不希望出现多行内容的情况

## 实际应用

目前已在以下接口中使用：

1. `NewStudentDataController.exportStudentData()` - 导出新生数据
2. `NewStudentDataController.exportInfoCollection()` - 导出新生基本信息采集数据  
3. `NewStudentDataController.exportTrafficInfo()` - 导出新生交通信息数据

## 效果对比

### 使用前（AutoSizeHandler）
- 列宽根据最长内容调整，可能出现极宽的列
- 整体布局不均匀，影响阅读体验
- 在有限页面宽度内可能无法显示所有列

### 使用后（HeaderWidthContentHeightHandler）
- 列宽根据表头文字调整，保持相对固定
- 长内容通过增加行高和自动换行显示
- 整体布局更加均匀美观
- 能在有限页面宽度内显示更多列

## 注意事项

1. **行高限制**：建议设置合理的最大行高，避免单行内容过长影响阅读
2. **字符数阈值**：根据实际列宽和字体大小调整每行字符数阈值
3. **中文优化**：默认启用中文字符优化，中文字符按2个字符宽度计算
4. **兼容性**：完全兼容EasyExcel 3.1.1和Apache POI 4.1.2

## 版本历史

- v1.0.0 (2025/7/6)
  - 初始版本
  - 支持表头宽度自适应
  - 支持内容高度自适应和自动换行
  - 支持中文字符优化
  - 提供多种配置选项
