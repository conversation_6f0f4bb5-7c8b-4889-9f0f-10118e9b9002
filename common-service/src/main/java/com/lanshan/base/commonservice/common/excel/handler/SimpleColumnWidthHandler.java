package com.lanshan.base.commonservice.common.excel.handler;

import com.alibaba.excel.write.handler.WorkbookWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteWorkbookHolder;
import org.apache.poi.ss.usermodel.*;

/**
 * 简化版列宽处理器
 * 
 * 专门用于大数据量场景，只设置固定列宽，不进行复杂计算
 * 性能优先，牺牲部分美观度
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/7/6
 */
public class SimpleColumnWidthHandler implements WorkbookWriteHandler {

    /**
     * 默认列宽（字符数）
     */
    private static final int DEFAULT_COLUMN_WIDTH = 15;

    /**
     * 固定列宽
     */
    private final int columnWidth;

    /**
     * 默认构造函数
     */
    public SimpleColumnWidthHandler() {
        this(DEFAULT_COLUMN_WIDTH);
    }

    /**
     * 自定义列宽构造函数
     * 
     * @param columnWidth 列宽（字符数）
     */
    public SimpleColumnWidthHandler(int columnWidth) {
        this.columnWidth = columnWidth;
    }

    @Override
    public void afterWorkbookDispose(WriteWorkbookHolder writeWorkbookHolder) {
        // 为所有工作表设置固定列宽
        Workbook workbook = writeWorkbookHolder.getWorkbook();
        for (int i = 0; i < workbook.getNumberOfSheets(); i++) {
            Sheet sheet = workbook.getSheetAt(i);
            setFixedColumnWidth(sheet);
        }
    }

    /**
     * 设置固定列宽
     */
    private void setFixedColumnWidth(Sheet sheet) {
        if (sheet.getLastRowNum() < 0) {
            return;
        }

        Row headerRow = sheet.getRow(0);
        if (headerRow == null) {
            return;
        }

        // 为所有列设置相同的固定宽度
        int lastCellNum = headerRow.getLastCellNum();
        for (int columnIndex = 0; columnIndex < lastCellNum; columnIndex++) {
            sheet.setColumnWidth(columnIndex, columnWidth * 256);
        }
    }

    /**
     * 创建默认配置的简化处理器
     */
    public static SimpleColumnWidthHandler create() {
        return new SimpleColumnWidthHandler();
    }

    /**
     * 创建自定义列宽的简化处理器
     */
    public static SimpleColumnWidthHandler create(int columnWidth) {
        return new SimpleColumnWidthHandler(columnWidth);
    }
}
