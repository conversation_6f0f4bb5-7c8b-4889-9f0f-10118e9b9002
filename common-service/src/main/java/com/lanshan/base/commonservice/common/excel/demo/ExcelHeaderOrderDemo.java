package com.lanshan.base.commonservice.common.excel.demo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

/**
 * Excel表头顺序控制演示
 * 
 * 展示不使用 index 属性的几种方法来控制表头顺序
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/7/6
 */
public class ExcelHeaderOrderDemo {

    /**
     * 方法一：通过字段声明顺序控制（推荐）
     * 
     * EasyExcel 会按照类中字段的声明顺序来排列表头
     * 这是最简单、最直观的方法
     */
    @Data
    public static class StudentExportByFieldOrder {
        
        // 字段按照期望的表头顺序声明
        @ExcelProperty("姓名")
        private String name;
        
        @ExcelProperty("性别")
        private String gender;
        
        @ExcelProperty("学工号")
        private String studentId;
        
        @ExcelProperty("学院")
        private String college;
        
        @ExcelProperty("专业")
        private String major;
        
        @ExcelProperty("班级")
        private String className;
        
        // 如果是继承的子类，新增字段会排在父类字段之后
        @ExcelProperty("备注")
        private String remark;
    }

    /**
     * 方法二：使用接口定义表头顺序
     * 
     * 通过接口常量定义表头名称，确保一致性
     */
    public interface StudentHeaders {
        String NAME = "姓名";
        String GENDER = "性别";
        String STUDENT_ID = "学工号";
        String COLLEGE = "学院";
        String MAJOR = "专业";
        String CLASS_NAME = "班级";
    }
    
    @Data
    public static class StudentExportWithInterface {
        
        @ExcelProperty(StudentHeaders.NAME)
        private String name;
        
        @ExcelProperty(StudentHeaders.GENDER)
        private String gender;
        
        @ExcelProperty(StudentHeaders.STUDENT_ID)
        private String studentId;
        
        @ExcelProperty(StudentHeaders.COLLEGE)
        private String college;
        
        @ExcelProperty(StudentHeaders.MAJOR)
        private String major;
        
        @ExcelProperty(StudentHeaders.CLASS_NAME)
        private String className;
    }

    /**
     * 方法三：使用枚举定义表头
     * 
     * 更加类型安全的方式
     */
    public enum StudentHeaderEnum {
        NAME("姓名"),
        GENDER("性别"),
        STUDENT_ID("学工号"),
        COLLEGE("学院"),
        MAJOR("专业"),
        CLASS_NAME("班级");
        
        private final String headerName;
        
        StudentHeaderEnum(String headerName) {
            this.headerName = headerName;
        }
        
        public String getHeaderName() {
            return headerName;
        }
    }
    
    @Data
    public static class StudentExportWithEnum {
        
        @ExcelProperty(value = "姓名") // 或者使用 StudentHeaderEnum.NAME.getHeaderName()
        private String name;
        
        @ExcelProperty(value = "性别")
        private String gender;
        
        @ExcelProperty(value = "学工号")
        private String studentId;
        
        @ExcelProperty(value = "学院")
        private String college;
        
        @ExcelProperty(value = "专业")
        private String major;
        
        @ExcelProperty(value = "班级")
        private String className;
    }

    /**
     * 方法四：使用组合模式
     * 
     * 将相关字段分组，便于管理
     */
    @Data
    public static class StudentBasicInfo {
        @ExcelProperty("姓名")
        private String name;
        
        @ExcelProperty("性别")
        private String gender;
        
        @ExcelProperty("学工号")
        private String studentId;
    }
    
    @Data
    public static class StudentAcademicInfo {
        @ExcelProperty("学院")
        private String college;
        
        @ExcelProperty("专业")
        private String major;
        
        @ExcelProperty("班级")
        private String className;
    }
    
    // 组合使用（需要手动处理字段顺序）
    @Data
    public static class StudentExportComposite {
        // 基本信息字段
        @ExcelProperty("姓名")
        private String name;
        
        @ExcelProperty("性别")
        private String gender;
        
        @ExcelProperty("学工号")
        private String studentId;
        
        // 学术信息字段
        @ExcelProperty("学院")
        private String college;
        
        @ExcelProperty("专业")
        private String major;
        
        @ExcelProperty("班级")
        private String className;
    }

    /**
     * 方法五：使用注解组合
     * 
     * 创建自定义注解来简化配置
     */
    @ExcelProperty("姓名")
    @ColumnWidth(15)
    public @interface StudentName {
    }
    
    @ExcelProperty("性别")
    @ColumnWidth(10)
    public @interface StudentGender {
    }
    
    @Data
    public static class StudentExportWithCustomAnnotations {
        
        @StudentName
        private String name;
        
        @StudentGender
        private String gender;
        
        @ExcelProperty("学工号")
        @ColumnWidth(20)
        private String studentId;
    }

    /**
     * 方法六：继承时的字段顺序控制
     * 
     * 当使用继承时，确保字段顺序正确
     */
    @Data
    public static class BaseStudentExport {
        // 基础字段按顺序声明
        @ExcelProperty("姓名")
        private String name;
        
        @ExcelProperty("性别")
        private String gender;
        
        @ExcelProperty("学工号")
        private String studentId;
    }
    
    @Data
    public static class ExtendedStudentExport extends BaseStudentExport {
        // 扩展字段会自动排在父类字段之后
        @ExcelProperty("学院")
        private String college;
        
        @ExcelProperty("专业")
        private String major;
        
        @ExcelProperty("错误信息")
        private String errorMsg;
    }
}
