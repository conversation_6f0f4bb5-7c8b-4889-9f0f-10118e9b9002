package com.lanshan.base.commonservice.common.excel.strategy;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.builder.ExcelWriterBuilder;
import com.lanshan.base.commonservice.common.excel.handler.HeaderWidthContentHeightHandler;
import com.lanshan.base.commonservice.common.excel.handler.SimpleColumnWidthHandler;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.List;

/**
 * Excel导出策略工具类
 * 
 * 根据数据量自动选择最优的导出策略
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/7/6
 */
public class ExcelExportStrategy {

    /**
     * 小数据量阈值（行数）
     */
    private static final int SMALL_DATA_THRESHOLD = 1000;

    /**
     * 中等数据量阈值（行数）
     */
    private static final int MEDIUM_DATA_THRESHOLD = 10000;

    /**
     * 大数据量阈值（行数）
     */
    private static final int LARGE_DATA_THRESHOLD = 50000;

    /**
     * 数据量级别枚举
     */
    public enum DataSize {
        SMALL,   // 小数据量：< 1000行
        MEDIUM,  // 中等数据量：1000-10000行
        LARGE,   // 大数据量：10000-50000行
        MASSIVE  // 超大数据量：> 50000行
    }

    /**
     * 导出策略枚举
     */
    public enum ExportStrategy {
        FULL_OPTIMIZATION,    // 完整优化：HeaderWidthContentHeightHandler
        SAMPLING_OPTIMIZATION, // 采样优化：HeaderWidthContentHeightHandler + 采样
        SIMPLE_OPTIMIZATION,   // 简单优化：SimpleColumnWidthHandler
        MINIMAL_PROCESSING     // 最小处理：无特殊处理器
    }

    /**
     * 根据数据量自动选择导出策略
     */
    public static ExportStrategy selectStrategy(int dataSize) {
        if (dataSize < SMALL_DATA_THRESHOLD) {
            return ExportStrategy.FULL_OPTIMIZATION;
        } else if (dataSize < MEDIUM_DATA_THRESHOLD) {
            return ExportStrategy.SAMPLING_OPTIMIZATION;
        } else if (dataSize < LARGE_DATA_THRESHOLD) {
            return ExportStrategy.SIMPLE_OPTIMIZATION;
        } else {
            return ExportStrategy.MINIMAL_PROCESSING;
        }
    }

    /**
     * 获取数据量级别
     */
    public static DataSize getDataSize(int rowCount) {
        if (rowCount < SMALL_DATA_THRESHOLD) {
            return DataSize.SMALL;
        } else if (rowCount < MEDIUM_DATA_THRESHOLD) {
            return DataSize.MEDIUM;
        } else if (rowCount < LARGE_DATA_THRESHOLD) {
            return DataSize.LARGE;
        } else {
            return DataSize.MASSIVE;
        }
    }

    /**
     * 智能导出Excel文件
     * 
     * @param response HTTP响应
     * @param dataClass 数据类型
     * @param dataList 数据列表
     * @param fileName 文件名
     * @param sheetName 工作表名
     */
    public static <T> void smartExport(HttpServletResponse response, Class<T> dataClass, 
                                      List<T> dataList, String fileName, String sheetName) throws IOException {
        // 设置响应头
        setupResponse(response, fileName);
        
        // 根据数据量选择策略
        ExportStrategy strategy = selectStrategy(dataList.size());
        
        // 执行导出
        executeExport(response.getOutputStream(), dataClass, dataList, sheetName, strategy);
    }

    /**
     * 手动指定策略导出Excel文件
     */
    public static <T> void exportWithStrategy(HttpServletResponse response, Class<T> dataClass,
                                            List<T> dataList, String fileName, String sheetName,
                                            ExportStrategy strategy) throws IOException {
        // 设置响应头
        setupResponse(response, fileName);
        
        // 执行导出
        executeExport(response.getOutputStream(), dataClass, dataList, sheetName, strategy);
    }

    /**
     * 执行导出逻辑
     */
    private static <T> void executeExport(OutputStream outputStream, Class<T> dataClass,
                                        List<T> dataList, String sheetName, ExportStrategy strategy) {
        ExcelWriterBuilder builder = EasyExcel.write(outputStream, dataClass);
        
        // 根据策略配置处理器
        switch (strategy) {
            case FULL_OPTIMIZATION:
                builder.registerWriteHandler(new HeaderWidthContentHeightHandler());
                break;
                
            case SAMPLING_OPTIMIZATION:
                // 采样优化：只分析前1000行
                builder.registerWriteHandler(HeaderWidthContentHeightHandler.createForLargeData(1000));
                break;
                
            case SIMPLE_OPTIMIZATION:
                // 简单优化：固定列宽
                builder.registerWriteHandler(SimpleColumnWidthHandler.create(18));
                break;
                
            case MINIMAL_PROCESSING:
                // 最小处理：不添加任何处理器
                break;
        }
        
        // 执行写入
        builder.sheet(sheetName).doWrite(dataList);
    }

    /**
     * 设置HTTP响应头
     */
    private static void setupResponse(HttpServletResponse response, String fileName) throws IOException {
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        String encodedFileName = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + encodedFileName + ".xlsx");
    }

    /**
     * 获取策略描述
     */
    public static String getStrategyDescription(ExportStrategy strategy) {
        switch (strategy) {
            case FULL_OPTIMIZATION:
                return "完整优化 - 智能列宽+行高自适应（适用于<1000行）";
            case SAMPLING_OPTIMIZATION:
                return "采样优化 - 基于前1000行计算列宽（适用于1000-10000行）";
            case SIMPLE_OPTIMIZATION:
                return "简单优化 - 固定列宽（适用于10000-50000行）";
            case MINIMAL_PROCESSING:
                return "最小处理 - 无特殊优化（适用于>50000行）";
            default:
                return "未知策略";
        }
    }

    /**
     * 打印策略建议
     */
    public static void printStrategyAdvice(int dataSize) {
        DataSize size = getDataSize(dataSize);
        ExportStrategy strategy = selectStrategy(dataSize);
        
        System.out.println("=== Excel导出策略建议 ===");
        System.out.println("数据量: " + dataSize + " 行");
        System.out.println("数据级别: " + getDataSizeDescription(size));
        System.out.println("推荐策略: " + getStrategyDescription(strategy));
        System.out.println("预估性能影响: " + getPerformanceImpact(strategy));
        System.out.println("========================");
    }

    /**
     * 获取数据量级别描述
     */
    private static String getDataSizeDescription(DataSize size) {
        switch (size) {
            case SMALL: return "小数据量";
            case MEDIUM: return "中等数据量";
            case LARGE: return "大数据量";
            case MASSIVE: return "超大数据量";
            default: return "未知";
        }
    }

    /**
     * 获取性能影响描述
     */
    private static String getPerformanceImpact(ExportStrategy strategy) {
        switch (strategy) {
            case FULL_OPTIMIZATION: return "轻微影响（+5-10%）";
            case SAMPLING_OPTIMIZATION: return "中等影响（+10-20%）";
            case SIMPLE_OPTIMIZATION: return "最小影响（+1-3%）";
            case MINIMAL_PROCESSING: return "无影响（0%）";
            default: return "未知";
        }
    }
}
