package com.lanshan.base.commonservice.welcomenewstudent.controller;


import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lanshan.base.api.utils.Result;
import com.lanshan.base.commonservice.common.excel.handler.AutoSizeHandler;
import com.lanshan.base.commonservice.common.excel.util.EasyExcelAutoSizeUtil;
import com.lanshan.base.commonservice.welcomenewstudent.converter.NewStudentDataConverter;
import com.lanshan.base.commonservice.welcomenewstudent.excel.NewStuInfoCollectionExportDTO;
import com.lanshan.base.commonservice.welcomenewstudent.excel.NewStudentExportDTO;
import com.lanshan.base.commonservice.welcomenewstudent.excel.NewStudentTrafficInfoExportDTO;
import com.lanshan.base.commonservice.welcomenewstudent.qo.NewStudentDataQO;
import com.lanshan.base.commonservice.welcomenewstudent.service.NewStudentDataService;
import com.lanshan.base.commonservice.welcomenewstudent.service.NewStudentInfoCollectionService;
import com.lanshan.base.commonservice.welcomenewstudent.service.NewStudentTrafficInfoService;
import com.lanshan.base.commonservice.welcomenewstudent.vo.DashboardVO;
import com.lanshan.base.commonservice.welcomenewstudent.vo.ImportResultVO;
import com.lanshan.base.commonservice.welcomenewstudent.vo.NewStudentDataVO;
import com.lanshan.base.commonservice.welcomenewstudent.vo.NewStudentStatVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.Serializable;
import java.net.URLEncoder;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 新生信息表(NewStudentData)表控制层
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("newStudentData")
@Api(tags = "新生信息表(NewStudentData)控制层", hidden = true)
public class NewStudentDataController {
    /**
     * 服务对象
     */
    @Resource
    private NewStudentDataService newStudentDataService;

    @Resource
    private NewStudentInfoCollectionService newStudentInfoCollectionService;

    @Resource
    private NewStudentTrafficInfoService newStudentTrafficInfoService;

    /**
     * 分页查询所有数据
     *
     * @param qo   查询实体
     * @return 所有数据
     */
    @ApiOperation("分页查询所有数据")
    @GetMapping("/page")
    public Result<IPage<NewStudentDataVO>> selectAll(NewStudentDataQO qo) {
        return Result.build(newStudentDataService.pageByParam(qo));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 单条数据
     */
    @ApiOperation("通过主键查询单条数据")
    @GetMapping("/selectOne/{id}")
    public Result<NewStudentDataVO> selectOne(@PathVariable Serializable id) {
        return Result.build(NewStudentDataConverter.INSTANCE.toVO(this.newStudentDataService.getById(id)));
    }

    /**
     * 新增数据
     *
     * @param vo 实体对象
     * @return 新增结果
     */
    @ApiOperation("新增数据")
    @PostMapping("/insert")
    public Result<Boolean> insert(@RequestBody NewStudentDataVO vo) {
        return Result.build(this.newStudentDataService.save(NewStudentDataConverter.INSTANCE.toEntity(vo)));
    }

    /**
     * 修改数据
     *
     * @param vo 实体对象
     * @return 修改结果
     */
    @ApiOperation("修改数据")
    @PostMapping("/update")
    public Result<Boolean> update(@RequestBody NewStudentDataVO vo) {
        return Result.build(this.newStudentDataService.updateById(NewStudentDataConverter.INSTANCE.toEntity(vo)));
    }

    /**
     * 删除数据
     *
     * @param idList 主键结合
     * @return 删除结果
     */
    @ApiOperation("删除数据")
    @PostMapping("/delete")
    public Result<Boolean> delete(@RequestBody List<Long> idList) {
        return Result.build(this.newStudentDataService.removeByIds(idList));
    }

    @ApiOperation("导入新生信息")
    @PostMapping("/importData")
    public Result<ImportResultVO> importData(MultipartFile file) {
        return Result.build(newStudentDataService.importData(file));
    }

    @ApiOperation("导出新生数据失败数据")
    @GetMapping("getImportNewStudentErrorData")
    public void getImportNewStudentErrorData(HttpServletResponse response) {
        newStudentDataService.exportErrorData(response);
    }

    @ApiOperation("获取新生统计信息")
    @GetMapping("/getNewStudentStat")
    public Result<List<NewStudentStatVO>> getNewStudentStat(String year) {
        return Result.build(newStudentDataService.getNewStudentStat(year));
    }

    @ApiOperation("获取当前人员信息")
    @GetMapping("/getCurrentUserInfo")
    public Result<NewStudentDataVO> getCurrentUserInfo(String userId) {
        return Result.build(newStudentDataService.getCurrentUserInfo(userId));
    }

    @ApiOperation("批量删除未报到的")
    @PostMapping("/batchDelNoReport")
    public Result<Boolean> batchDelNoReport() {
        return Result.build(newStudentDataService.batchDelNoReport());
    }

    @ApiOperation("转在校生")
    @PostMapping("/transferZxs")
    public Result<Boolean> transferZxs() {
        return Result.build(newStudentDataService.transferZxs());
    }

    @ApiOperation("导出新生数据")
    @PostMapping("/exportStudentData")
    public void exportStudentData(@RequestBody NewStudentDataQO qo, HttpServletResponse response) throws IOException {
        List<NewStudentDataVO> newStudentDataVOList = newStudentDataService.exportStudentData(qo);
        List<NewStudentExportDTO> exportDTOList = newStudentDataVOList.stream().map(newStudentDataVO -> {
            NewStudentExportDTO newStudentExportDTO = new NewStudentExportDTO();
            BeanUtils.copyProperties(newStudentDataVO, newStudentExportDTO);
            return newStudentExportDTO;
        }).collect(Collectors.toList());

        // 使用基础的自适应大小功能导出（避免表头处理器冲突）
        try {
            // 设置响应头
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("新生信息表", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");

            // 使用基础的自适应处理器
            EasyExcel.write(response.getOutputStream(), NewStudentExportDTO.class)
                    .registerWriteHandler(new AutoSizeHandler())
                    .sheet("新生信息表")
                    .doWrite(exportDTOList);

        } catch (IOException e) {
            throw new RuntimeException("导出Excel文件失败", e);
        }
    }

    @ApiOperation("导出新生基本信息采集数据")
    @PostMapping("/exportInfoCollection")
    public void exportInfoCollection(@RequestBody NewStudentDataQO qo, HttpServletResponse response) {
        // 获取导出数据
        List<NewStuInfoCollectionExportDTO> exportData = newStudentInfoCollectionService.exportData(qo);

        // 使用基础的自适应大小功能导出（避免表头处理器冲突）
        try {
            // 设置响应头
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("新生基本信息采集数据", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");

            // 使用基础的自适应处理器
            EasyExcel.write(response.getOutputStream(), NewStuInfoCollectionExportDTO.class)
                    .registerWriteHandler(new AutoSizeHandler())
                    .sheet("新生基本信息采集")
                    .doWrite(exportData);

        } catch (IOException e) {
            throw new RuntimeException("导出Excel文件失败", e);
        }
    }

    @ApiOperation("导出新生交通信息数据")
    @PostMapping("/exportTrafficInfo")
    public void exportTrafficInfo(@RequestBody NewStudentDataQO qo, HttpServletResponse response) {
        // 获取导出数据
        List<NewStudentTrafficInfoExportDTO> exportData = newStudentTrafficInfoService.exportData(qo);

        // 使用基础的自适应大小功能导出（避免表头处理器冲突）
        try {
            // 设置响应头
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("新生交通信息数据", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");

            // 使用基础的自适应处理器
            EasyExcel.write(response.getOutputStream(), NewStudentTrafficInfoExportDTO.class)
                    .registerWriteHandler(new AutoSizeHandler())
                    .sheet("新生交通信息")
                    .doWrite(exportData);

        } catch (IOException e) {
            throw new RuntimeException("导出Excel文件失败", e);
        }
    }

    @ApiOperation("获取数据大屏展示数据")
    @GetMapping("/dashboard")
    public Result<DashboardVO> getDashboardData(@RequestParam(required = false) String year) {
        DashboardVO dashboardData = newStudentDataService.getDashboardData(year);
        return Result.build(dashboardData);
    }
}

