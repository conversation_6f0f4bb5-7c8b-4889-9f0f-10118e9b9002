package com.lanshan.base.commonservice.common.excel.util;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.lanshan.base.commonservice.common.excel.handler.AutoColumnWidthHandler;
import com.lanshan.base.commonservice.common.excel.handler.AutoRowHeightHandler;
import com.lanshan.base.commonservice.common.excel.handler.AutoSizeHandler;
import com.lanshan.base.commonservice.common.excel.handler.AutoHeaderWidthHandler;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.List;

/**
 * EasyExcel自适应大小工具类
 * <p>
 * 提供便捷的方法来创建具有自适应列宽和行高功能的Excel文件
 * <p>
 * 使用示例：
 * 1. 基础用法：
 * EasyExcelAutoSizeUtil.writeWithAutoSize(response, "文件名", "工作表名", ExportDTO.class, dataList);
 * <p>
 * 2. 自定义配置：
 * EasyExcelAutoSizeUtil.builder()
 * .maxColumnWidth(60)
 * .minColumnWidth(10)
 * .maxRowHeight(120f)
 * .writeToResponse(response, "文件名", "工作表名", ExportDTO.class, dataList);
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/7/6
 */
public class EasyExcelAutoSizeUtil {

    /**
     * 写入Excel到HttpServletResponse（使用默认自适应配置）
     *
     * @param response  HTTP响应对象
     * @param fileName  文件名（不包含扩展名）
     * @param sheetName 工作表名
     * @param clazz     数据类型
     * @param data      数据列表
     * @param <T>       数据类型泛型
     */
    public static <T> void writeWithAutoSize(HttpServletResponse response, String fileName, String sheetName,
                                             Class<T> clazz, List<T> data) {
        try {
            setupResponse(response, fileName);

            EasyExcel.write(response.getOutputStream(), clazz)
                    .registerWriteHandler(new AutoSizeHandler())
                    .sheet(sheetName)
                    .doWrite(data);

        } catch (IOException e) {
            throw new RuntimeException("导出Excel文件失败", e);
        }
    }

    /**
     * 写入Excel到OutputStream（使用默认自适应配置）
     *
     * @param outputStream 输出流
     * @param sheetName    工作表名
     * @param clazz        数据类型
     * @param data         数据列表
     * @param <T>          数据类型泛型
     */
    public static <T> void writeWithAutoSize(OutputStream outputStream, String sheetName,
                                             Class<T> clazz, List<T> data) {
        EasyExcel.write(outputStream, clazz)
                .registerWriteHandler(new AutoSizeHandler())
                .sheet(sheetName)
                .doWrite(data);
    }

    /**
     * 只使用自适应列宽
     *
     * @param response  HTTP响应对象
     * @param fileName  文件名
     * @param sheetName 工作表名
     * @param clazz     数据类型
     * @param data      数据列表
     * @param <T>       数据类型泛型
     */
    public static <T> void writeWithAutoColumnWidth(HttpServletResponse response, String fileName, String sheetName,
                                                    Class<T> clazz, List<T> data) {
        try {
            setupResponse(response, fileName);

            EasyExcel.write(response.getOutputStream(), clazz)
                    .registerWriteHandler(new AutoColumnWidthHandler())
                    .sheet(sheetName)
                    .doWrite(data);

        } catch (IOException e) {
            throw new RuntimeException("导出Excel文件失败", e);
        }
    }

    /**
     * 只使用自适应行高
     *
     * @param response  HTTP响应对象
     * @param fileName  文件名
     * @param sheetName 工作表名
     * @param clazz     数据类型
     * @param data      数据列表
     * @param <T>       数据类型泛型
     */
    public static <T> void writeWithAutoRowHeight(HttpServletResponse response, String fileName, String sheetName,
                                                  Class<T> clazz, List<T> data) {
        try {
            setupResponse(response, fileName);

            EasyExcel.write(response.getOutputStream(), clazz)
                    .registerWriteHandler(new AutoRowHeightHandler())
                    .sheet(sheetName)
                    .doWrite(data);

        } catch (IOException e) {
            throw new RuntimeException("导出Excel文件失败", e);
        }
    }

    /**
     * 只使用表头自适应宽度
     *
     * @param response  HTTP响应对象
     * @param fileName  文件名
     * @param sheetName 工作表名
     * @param clazz     数据类型
     * @param data      数据列表
     * @param <T>       数据类型泛型
     */
    public static <T> void writeWithAutoHeaderWidth(HttpServletResponse response, String fileName, String sheetName,
                                                    Class<T> clazz, List<T> data) {
        try {
            setupResponse(response, fileName);

            EasyExcel.write(response.getOutputStream(), clazz)
                    .registerWriteHandler(new AutoHeaderWidthHandler())
                    .sheet(sheetName)
                    .doWrite(data);

        } catch (IOException e) {
            throw new RuntimeException("导出Excel文件失败", e);
        }
    }

    /**
     * 只使用表头自适应宽度（输出流版本）
     *
     * @param outputStream 输出流
     * @param sheetName    工作表名
     * @param clazz        数据类型
     * @param data         数据列表
     * @param <T>          数据类型泛型
     */
    public static <T> void writeWithAutoHeaderWidth(OutputStream outputStream, String sheetName,
                                                    Class<T> clazz, List<T> data) {
        EasyExcel.write(outputStream, clazz)
                .registerWriteHandler(new AutoHeaderWidthHandler())
                .sheet(sheetName)
                .doWrite(data);
    }

    /**
     * 创建构建器
     *
     * @return AutoSizeBuilder实例
     */
    public static AutoSizeBuilder builder() {
        return new AutoSizeBuilder();
    }

    /**
     * 设置HTTP响应头
     *
     * @param response HTTP响应对象
     * @param fileName 文件名
     */
    private static void setupResponse(HttpServletResponse response, String fileName) {
        try {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");

            String encodedFileName = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + encodedFileName + ".xlsx");

        } catch (Exception e) {
            throw new RuntimeException("设置响应头失败", e);
        }
    }

    /**
     * 自适应大小构建器
     */
    public static class AutoSizeBuilder {
        private AutoSizeHandler.ColumnWidthConfig columnConfig = new AutoSizeHandler.ColumnWidthConfig();
        private AutoSizeHandler.RowHeightConfig rowConfig = new AutoSizeHandler.RowHeightConfig();
        private HeaderWidthConfig headerConfig = new HeaderWidthConfig();

        /**
         * 设置最大列宽
         */
        public AutoSizeBuilder maxColumnWidth(int maxColumnWidth) {
            this.columnConfig.setMaxColumnWidth(maxColumnWidth);
            return this;
        }

        /**
         * 设置最小列宽
         */
        public AutoSizeBuilder minColumnWidth(int minColumnWidth) {
            this.columnConfig.setMinColumnWidth(minColumnWidth);
            return this;
        }

        /**
         * 设置是否启用中文字符优化
         */
        public AutoSizeBuilder enableChineseOptimization(boolean enable) {
            this.columnConfig.setEnableChineseOptimization(enable);
            return this;
        }

        /**
         * 设置最大行高
         */
        public AutoSizeBuilder maxRowHeight(float maxRowHeight) {
            this.rowConfig.setMaxRowHeight(maxRowHeight);
            return this;
        }

        /**
         * 设置最小行高
         */
        public AutoSizeBuilder minRowHeight(float minRowHeight) {
            this.rowConfig.setMinRowHeight(minRowHeight);
            return this;
        }

        /**
         * 设置默认行高
         */
        public AutoSizeBuilder defaultRowHeight(float defaultRowHeight) {
            this.rowConfig.setDefaultRowHeight(defaultRowHeight);
            return this;
        }

        /**
         * 设置标题行高
         */
        public AutoSizeBuilder headerRowHeight(float headerRowHeight) {
            this.rowConfig.setHeaderRowHeight(headerRowHeight);
            return this;
        }

        /**
         * 设置是否启用自动换行
         */
        public AutoSizeBuilder enableAutoWrap(boolean enable) {
            this.rowConfig.setEnableAutoWrap(enable);
            return this;
        }

        /**
         * 设置最大表头列宽
         */
        public AutoSizeBuilder maxHeaderWidth(int maxHeaderWidth) {
            this.headerConfig.setMaxHeaderWidth(maxHeaderWidth);
            return this;
        }

        /**
         * 设置最小表头列宽
         */
        public AutoSizeBuilder minHeaderWidth(int minHeaderWidth) {
            this.headerConfig.setMinHeaderWidth(minHeaderWidth);
            return this;
        }

        /**
         * 设置表头额外宽度
         */
        public AutoSizeBuilder headerExtraWidth(int headerExtraWidth) {
            this.headerConfig.setHeaderExtraWidth(headerExtraWidth);
            return this;
        }

        /**
         * 设置是否启用表头中文字符优化
         */
        public AutoSizeBuilder enableHeaderChineseOptimization(boolean enable) {
            this.headerConfig.setEnableChineseOptimization(enable);
            return this;
        }

        /**
         * 设置是否只调整表头宽度
         */
        public AutoSizeBuilder headerOnly(boolean headerOnly) {
            this.headerConfig.setHeaderOnly(headerOnly);
            return this;
        }

        /**
         * 写入到HttpServletResponse
         */
        public <T> void writeToResponse(HttpServletResponse response, String fileName, String sheetName,
                                        Class<T> clazz, List<T> data) {
            try {
                setupResponse(response, fileName);

                // 只注册AutoSizeHandler，它已经包含了列宽和行高处理
                // 表头宽度处理集成到AutoSizeHandler中
                EasyExcel.write(response.getOutputStream(), clazz)
                        .registerWriteHandler(createEnhancedAutoSizeHandler())
                        .sheet(sheetName)
                        .doWrite(data);

            } catch (IOException e) {
                throw new RuntimeException("导出Excel文件失败", e);
            }
        }

        /**
         * 写入到OutputStream
         */
        public <T> void writeToStream(OutputStream outputStream, String sheetName, Class<T> clazz, List<T> data) {
            EasyExcel.write(outputStream, clazz)
                    .registerWriteHandler(new AutoSizeHandler(columnConfig, rowConfig))
                    .registerWriteHandler(createHeaderWidthHandler())
                    .sheet(sheetName)
                    .doWrite(data);
        }

        /**
         * 创建ExcelWriter（用于更复杂的场景）
         */
        public ExcelWriter createWriter(OutputStream outputStream) {
            return EasyExcel.write(outputStream)
                    .registerWriteHandler(new AutoSizeHandler(columnConfig, rowConfig))
                    .registerWriteHandler(createHeaderWidthHandler())
                    .build();
        }

        /**
         * 获取配置好的AutoSizeHandler
         */
        public AutoSizeHandler build() {
            return new AutoSizeHandler(columnConfig, rowConfig);
        }

        /**
         * 创建表头宽度处理器
         */
        private AutoHeaderWidthHandler createHeaderWidthHandler() {
            return new AutoHeaderWidthHandler(
                    headerConfig.getMaxHeaderWidth(),
                    headerConfig.getMinHeaderWidth(),
                    headerConfig.getHeaderExtraWidth(),
                    headerConfig.isEnableChineseOptimization(),
                    headerConfig.isHeaderOnly()
            );
        }
    }

    /**
     * 表头宽度配置类
     */
    public static class HeaderWidthConfig {
        private int maxHeaderWidth = 30;
        private int minHeaderWidth = 8;
        private int headerExtraWidth = 2;
        private boolean enableChineseOptimization = true;
        private boolean headerOnly = true;

        public HeaderWidthConfig() {
        }

        public HeaderWidthConfig(int maxHeaderWidth, int minHeaderWidth, int headerExtraWidth,
                                boolean enableChineseOptimization, boolean headerOnly) {
            this.maxHeaderWidth = maxHeaderWidth;
            this.minHeaderWidth = minHeaderWidth;
            this.headerExtraWidth = headerExtraWidth;
            this.enableChineseOptimization = enableChineseOptimization;
            this.headerOnly = headerOnly;
        }

        // Getters and Setters
        public int getMaxHeaderWidth() {
            return maxHeaderWidth;
        }

        public void setMaxHeaderWidth(int maxHeaderWidth) {
            this.maxHeaderWidth = maxHeaderWidth;
        }

        public int getMinHeaderWidth() {
            return minHeaderWidth;
        }

        public void setMinHeaderWidth(int minHeaderWidth) {
            this.minHeaderWidth = minHeaderWidth;
        }

        public int getHeaderExtraWidth() {
            return headerExtraWidth;
        }

        public void setHeaderExtraWidth(int headerExtraWidth) {
            this.headerExtraWidth = headerExtraWidth;
        }

        public boolean isEnableChineseOptimization() {
            return enableChineseOptimization;
        }

        public void setEnableChineseOptimization(boolean enableChineseOptimization) {
            this.enableChineseOptimization = enableChineseOptimization;
        }

        public boolean isHeaderOnly() {
            return headerOnly;
        }

        public void setHeaderOnly(boolean headerOnly) {
            this.headerOnly = headerOnly;
        }
    }
}
