package com.lanshan.base.commonservice.welcomenewstudent.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.lanshan.base.commonservice.welcomenewstudent.excel.convert.IdentityTypeConverter;
import com.lanshan.base.commonservice.welcomenewstudent.excel.convert.ReportFlagTypeConverter;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
public class NewStudentImportDTO {

    @ExcelProperty("姓名")
    private String name;

    @ExcelProperty(value = "性别")
    private String gender;

    @ExcelProperty("生源地")
    private String sourceOfOrigin;

    @ExcelProperty("学工号")
    private String xh;

    @ExcelProperty("准考证号")
    private String zkzh;

    @ExcelProperty(value = "证件类型", converter = IdentityTypeConverter.class)
    private String identityType;

    @ExcelProperty("证件号码")
    private String idCardNum;

    @ExcelProperty("手机号")
    private String phoneNo;

    @ExcelProperty("学院编码")
    private String collegeCode;

    @ExcelProperty("学院名称")
    private String collegeName;

    @ExcelProperty("专业编码")
    private String majorCode;

    @ExcelProperty("专业名称")
    private String majorName;

    @ExcelProperty("班级编码")
    private String classCode;

    @ExcelProperty("班级名称")
    private String className;

    @ExcelProperty("楼栋编码")
    private String buildingCode;

    @ExcelProperty("楼栋名称")
    private String buildingName;

    @ExcelProperty("房间号")
    private String roomNumber;

    @ExcelProperty("床位号")
    private String bedNumber;

    @ExcelProperty(value = "学生类型")
    private String stuType;

    @ExcelProperty(value = "报道状态", converter = ReportFlagTypeConverter.class)
    private Boolean reportFlag;

    @ExcelProperty("错误信息")
    private String errorMsg;
}

