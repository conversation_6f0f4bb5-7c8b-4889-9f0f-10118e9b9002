package com.lanshan.base.commonservice.common.excel.demo;

import com.alibaba.excel.EasyExcel;
import com.lanshan.base.commonservice.common.excel.handler.HeaderWidthContentHeightHandler;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.List;

/**
 * 智能列宽自适应 + 内容高度自适应功能演示
 *
 * 功能特点：
 * 1. 智能列宽决策：比较表头和内容宽度，取较长者作为列宽（不超过最大值）
 * 2. 内容高度自适应：超过列宽的内容通过增加行高来显示
 * 3. 自动换行：超过列宽的内容（表头或数据）都自动换行显示
 * 4. 中文字符优化：中文字符按2个字符宽度计算
 *
 * 列宽决策逻辑：
 * - 如果内容比表头长且未达到最大值，使用内容宽度
 * - 如果表头比内容长且未达到最大值，使用表头宽度
 * - 如果都超过最大值，使用最大值，超出部分换行显示
 * - 如果都小于最小值，使用最小值
 *
 * <AUTHOR>
 * @version 2.0.0
 * @date 2025/7/6
 */
public class HeaderWidthContentHeightDemo {

    /**
     * 方法一：使用默认配置（推荐）
     * 
     * 默认配置：
     * - 列最大宽度：30字符
     * - 列最小宽度：8字符
     * - 数据行最大高度：120磅
     * - 数据行最小高度：12磅
     * - 表头行高：25磅
     * - 每行字符数阈值：30字符（超过会换行）
     * - 启用中文字符优化
     */
    public static <T> void exportWithDefaultConfig(HttpServletResponse response, 
                                                  String fileName, String sheetName,
                                                  Class<T> clazz, List<T> data) throws IOException {
        // 设置响应头
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        String encodedFileName = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + encodedFileName + ".xlsx");

        // 使用默认配置的处理器
        EasyExcel.write(response.getOutputStream(), clazz)
                .registerWriteHandler(new HeaderWidthContentHeightHandler())
                .sheet(sheetName)
                .doWrite(data);
    }

    /**
     * 方法二：自定义列宽配置
     *
     * 适用场景：表头或内容特别长或特别短的情况
     */
    public static <T> void exportWithCustomColumnWidth(HttpServletResponse response,
                                                      String fileName, String sheetName,
                                                      Class<T> clazz, List<T> data,
                                                      int maxColumnWidth, int minColumnWidth) throws IOException {
        // 设置响应头
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        String encodedFileName = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + encodedFileName + ".xlsx");

        // 使用自定义列宽配置的处理器
        EasyExcel.write(response.getOutputStream(), clazz)
                .registerWriteHandler(HeaderWidthContentHeightHandler.createWithColumnConfig(maxColumnWidth, minColumnWidth))
                .sheet(sheetName)
                .doWrite(data);
    }

    /**
     * 方法三：自定义行高配置
     * 
     * 适用场景：内容特别长，需要更大的行高限制
     */
    public static <T> void exportWithCustomRowHeight(HttpServletResponse response, 
                                                    String fileName, String sheetName,
                                                    Class<T> clazz, List<T> data,
                                                    float maxRowHeight, float minRowHeight) throws IOException {
        // 设置响应头
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        String encodedFileName = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + encodedFileName + ".xlsx");

        // 使用自定义行高配置的处理器
        EasyExcel.write(response.getOutputStream(), clazz)
                .registerWriteHandler(HeaderWidthContentHeightHandler.createWithRowHeightConfig(maxRowHeight, minRowHeight))
                .sheet(sheetName)
                .doWrite(data);
    }

    /**
     * 方法四：完全自定义配置
     * 
     * 适用场景：需要精确控制所有参数的情况
     */
    public static <T> void exportWithFullCustomConfig(HttpServletResponse response, 
                                                     String fileName, String sheetName,
                                                     Class<T> clazz, List<T> data) throws IOException {
        // 设置响应头
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        String encodedFileName = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + encodedFileName + ".xlsx");

        // 创建完全自定义配置的处理器
        HeaderWidthContentHeightHandler handler = new HeaderWidthContentHeightHandler(
                40,    // 表头最大宽度（字符数）
                10,    // 表头最小宽度（字符数）
                3,     // 表头额外宽度（字符数）
                150f,  // 数据行最大高度（磅）
                15f,   // 数据行最小高度（磅）
                30f,   // 表头行高（磅）
                18f,   // 默认行高（磅）
                18f,   // 每行文字高度（磅）
                25,    // 每行字符数阈值
                true   // 启用中文字符优化
        );

        EasyExcel.write(response.getOutputStream(), clazz)
                .registerWriteHandler(handler)
                .sheet(sheetName)
                .doWrite(data);
    }

    /**
     * 使用说明和最佳实践
     */
    public static void main(String[] args) {
        System.out.println("=== HeaderWidthContentHeightHandler 使用说明 ===");
        System.out.println();
        
        System.out.println("功能特点：");
        System.out.println("1. 智能列宽决策：比较表头和内容宽度，取较长者作为列宽（不超过最大值）");
        System.out.println("2. 内容高度自适应：超过列宽的内容通过增加行高来显示");
        System.out.println("3. 自动换行：超过列宽的内容（表头或数据）都自动换行显示");
        System.out.println("4. 中文字符优化：中文字符按2个字符宽度计算，显示更准确");
        System.out.println();

        System.out.println("列宽决策逻辑：");
        System.out.println("- 如果内容比表头长且未达到最大值，使用内容宽度");
        System.out.println("- 如果表头比内容长且未达到最大值，使用表头宽度");
        System.out.println("- 如果都超过最大值，使用最大值，超出部分换行显示");
        System.out.println("- 如果都小于最小值，使用最小值");
        System.out.println();

        System.out.println("与传统AutoSizeHandler的区别：");
        System.out.println("- AutoSizeHandler：根据所有内容调整列宽，可能导致某些列过宽");
        System.out.println("- HeaderWidthContentHeightHandler：智能决策列宽，超出部分通过增加行高显示");
        System.out.println();
        
        System.out.println("适用场景：");
        System.out.println("✅ 表头和内容长度不一致，需要智能决策列宽的情况");
        System.out.println("✅ 希望列宽既不会太窄也不会太宽的情况");
        System.out.println("✅ 需要在有限的页面宽度内显示更多列的情况");
        System.out.println("✅ 内容包含长文本、地址、描述等信息的情况");
        System.out.println("✅ 表头或内容可能超过理想宽度，需要换行显示的情况");
        System.out.println();
        
        System.out.println("配置建议：");
        System.out.println("- 列最大宽度：20-40字符（根据实际表头和内容长度调整）");
        System.out.println("- 列最小宽度：8-12字符（保证基本可读性）");
        System.out.println("- 数据行最大高度：100-150磅（避免行高过大影响阅读）");
        System.out.println("- 每行字符数阈值：20-40字符（根据列宽和字体大小调整）");
        System.out.println();
        
        System.out.println("推荐使用方法一（默认配置），已经能满足大部分需求。");
        System.out.println("如有特殊需求，可以使用其他方法进行自定义配置。");
    }
}
