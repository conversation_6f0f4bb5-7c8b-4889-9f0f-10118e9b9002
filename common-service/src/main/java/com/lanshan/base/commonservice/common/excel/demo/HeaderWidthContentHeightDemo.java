package com.lanshan.base.commonservice.common.excel.demo;

import com.alibaba.excel.EasyExcel;
import com.lanshan.base.commonservice.common.excel.handler.HeaderWidthContentHeightHandler;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.List;

/**
 * 表头宽度自适应 + 内容高度自适应功能演示
 * 
 * 功能特点：
 * 1. 表头宽度自适应：根据表头文字长度调整列宽
 * 2. 内容高度自适应：当内容很长时，通过增加行高来显示，而不是增加列宽
 * 3. 自动换行：长内容自动换行显示
 * 4. 中文字符优化：中文字符按2个字符宽度计算
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/7/6
 */
public class HeaderWidthContentHeightDemo {

    /**
     * 方法一：使用默认配置（推荐）
     * 
     * 默认配置：
     * - 表头最大宽度：30字符
     * - 表头最小宽度：8字符
     * - 数据行最大高度：120磅
     * - 数据行最小高度：12磅
     * - 表头行高：25磅
     * - 每行字符数阈值：30字符（超过会换行）
     * - 启用中文字符优化
     */
    public static <T> void exportWithDefaultConfig(HttpServletResponse response, 
                                                  String fileName, String sheetName,
                                                  Class<T> clazz, List<T> data) throws IOException {
        // 设置响应头
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        String encodedFileName = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + encodedFileName + ".xlsx");

        // 使用默认配置的处理器
        EasyExcel.write(response.getOutputStream(), clazz)
                .registerWriteHandler(new HeaderWidthContentHeightHandler())
                .sheet(sheetName)
                .doWrite(data);
    }

    /**
     * 方法二：自定义表头宽度配置
     * 
     * 适用场景：表头文字特别长或特别短的情况
     */
    public static <T> void exportWithCustomHeaderWidth(HttpServletResponse response, 
                                                      String fileName, String sheetName,
                                                      Class<T> clazz, List<T> data,
                                                      int maxHeaderWidth, int minHeaderWidth) throws IOException {
        // 设置响应头
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        String encodedFileName = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + encodedFileName + ".xlsx");

        // 使用自定义表头宽度配置的处理器
        EasyExcel.write(response.getOutputStream(), clazz)
                .registerWriteHandler(HeaderWidthContentHeightHandler.createWithHeaderConfig(maxHeaderWidth, minHeaderWidth))
                .sheet(sheetName)
                .doWrite(data);
    }

    /**
     * 方法三：自定义行高配置
     * 
     * 适用场景：内容特别长，需要更大的行高限制
     */
    public static <T> void exportWithCustomRowHeight(HttpServletResponse response, 
                                                    String fileName, String sheetName,
                                                    Class<T> clazz, List<T> data,
                                                    float maxRowHeight, float minRowHeight) throws IOException {
        // 设置响应头
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        String encodedFileName = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + encodedFileName + ".xlsx");

        // 使用自定义行高配置的处理器
        EasyExcel.write(response.getOutputStream(), clazz)
                .registerWriteHandler(HeaderWidthContentHeightHandler.createWithRowHeightConfig(maxRowHeight, minRowHeight))
                .sheet(sheetName)
                .doWrite(data);
    }

    /**
     * 方法四：完全自定义配置
     * 
     * 适用场景：需要精确控制所有参数的情况
     */
    public static <T> void exportWithFullCustomConfig(HttpServletResponse response, 
                                                     String fileName, String sheetName,
                                                     Class<T> clazz, List<T> data) throws IOException {
        // 设置响应头
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        String encodedFileName = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + encodedFileName + ".xlsx");

        // 创建完全自定义配置的处理器
        HeaderWidthContentHeightHandler handler = new HeaderWidthContentHeightHandler(
                40,    // 表头最大宽度（字符数）
                10,    // 表头最小宽度（字符数）
                3,     // 表头额外宽度（字符数）
                150f,  // 数据行最大高度（磅）
                15f,   // 数据行最小高度（磅）
                30f,   // 表头行高（磅）
                18f,   // 默认行高（磅）
                18f,   // 每行文字高度（磅）
                25,    // 每行字符数阈值
                true   // 启用中文字符优化
        );

        EasyExcel.write(response.getOutputStream(), clazz)
                .registerWriteHandler(handler)
                .sheet(sheetName)
                .doWrite(data);
    }

    /**
     * 使用说明和最佳实践
     */
    public static void main(String[] args) {
        System.out.println("=== HeaderWidthContentHeightHandler 使用说明 ===");
        System.out.println();
        
        System.out.println("功能特点：");
        System.out.println("1. 表头宽度自适应：根据表头文字长度自动调整列宽");
        System.out.println("2. 内容高度自适应：当内容很长时，通过增加行高来显示");
        System.out.println("3. 自动换行：长内容自动换行显示，保持表格整洁");
        System.out.println("4. 中文字符优化：中文字符按2个字符宽度计算，显示更准确");
        System.out.println();
        
        System.out.println("与传统AutoSizeHandler的区别：");
        System.out.println("- AutoSizeHandler：根据所有内容（表头+数据）调整列宽");
        System.out.println("- HeaderWidthContentHeightHandler：只根据表头调整列宽，长内容通过增加行高显示");
        System.out.println();
        
        System.out.println("适用场景：");
        System.out.println("✅ 表头文字长度适中，但数据内容可能很长的情况");
        System.out.println("✅ 希望保持表格列宽一致，避免某些列过宽的情况");
        System.out.println("✅ 需要在有限的页面宽度内显示更多列的情况");
        System.out.println("✅ 内容包含长文本、地址、描述等信息的情况");
        System.out.println();
        
        System.out.println("配置建议：");
        System.out.println("- 表头最大宽度：20-40字符（根据实际表头长度调整）");
        System.out.println("- 表头最小宽度：8-12字符（保证基本可读性）");
        System.out.println("- 数据行最大高度：100-150磅（避免行高过大影响阅读）");
        System.out.println("- 每行字符数阈值：20-40字符（根据列宽和字体大小调整）");
        System.out.println();
        
        System.out.println("推荐使用方法一（默认配置），已经能满足大部分需求。");
        System.out.println("如有特殊需求，可以使用其他方法进行自定义配置。");
    }
}
