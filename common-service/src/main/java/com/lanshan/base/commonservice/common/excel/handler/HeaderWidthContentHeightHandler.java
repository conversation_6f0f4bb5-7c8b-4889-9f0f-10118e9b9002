package com.lanshan.base.commonservice.common.excel.handler;

import com.alibaba.excel.write.handler.RowWriteHandler;
import com.alibaba.excel.write.handler.WorkbookWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteTableHolder;
import com.alibaba.excel.write.metadata.holder.WriteWorkbookHolder;
import org.apache.poi.ss.usermodel.*;

import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 表头宽度自适应 + 内容高度自适应处理器
 * 
 * 功能特点：
 * 1. 表头宽度自适应：根据表头文字长度调整列宽
 * 2. 内容高度自适应：当内容很长时，通过增加行高来显示，而不是增加列宽
 * 3. 自动换行：长内容自动换行显示
 * 4. 中文字符优化：中文字符按2个字符宽度计算
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/7/6
 */
public class HeaderWidthContentHeightHandler implements WorkbookWriteHandler, RowWriteHandler {

    /**
     * 默认最大表头列宽（字符数）
     */
    private static final int DEFAULT_MAX_HEADER_WIDTH = 30;

    /**
     * 默认最小表头列宽（字符数）
     */
    private static final int DEFAULT_MIN_HEADER_WIDTH = 8;

    /**
     * 表头额外宽度（字符数）- 为了美观留出的额外空间
     */
    private static final int HEADER_EXTRA_WIDTH = 2;

    /**
     * 默认行高（磅）
     */
    private static final float DEFAULT_ROW_HEIGHT = 15f;

    /**
     * 最大行高（磅）
     */
    private static final float MAX_ROW_HEIGHT = 120f;

    /**
     * 最小行高（磅）
     */
    private static final float MIN_ROW_HEIGHT = 12f;

    /**
     * 标题行高（磅）
     */
    private static final float HEADER_ROW_HEIGHT = 25f;

    /**
     * 每行文字对应的行高增量（磅）
     */
    private static final float HEIGHT_PER_LINE = 16f;

    /**
     * 每行字符数阈值（超过此数量会换行）
     */
    private static final int CHARS_PER_LINE = 30;

    // 配置参数
    private final int maxHeaderWidth;
    private final int minHeaderWidth;
    private final int headerExtraWidth;
    private final float maxRowHeight;
    private final float minRowHeight;
    private final float headerRowHeight;
    private final float defaultRowHeight;
    private final float heightPerLine;
    private final int charsPerLine;
    private final boolean enableChineseOptimization;

    /**
     * 标记是否已经处理过表头
     */
    private final AtomicBoolean headerProcessed = new AtomicBoolean(false);

    /**
     * 默认构造函数
     */
    public HeaderWidthContentHeightHandler() {
        this(DEFAULT_MAX_HEADER_WIDTH, DEFAULT_MIN_HEADER_WIDTH, HEADER_EXTRA_WIDTH,
             MAX_ROW_HEIGHT, MIN_ROW_HEIGHT, HEADER_ROW_HEIGHT, DEFAULT_ROW_HEIGHT,
             HEIGHT_PER_LINE, CHARS_PER_LINE, true);
    }

    /**
     * 自定义构造函数
     */
    public HeaderWidthContentHeightHandler(int maxHeaderWidth, int minHeaderWidth, int headerExtraWidth,
                                          float maxRowHeight, float minRowHeight, float headerRowHeight,
                                          float defaultRowHeight, float heightPerLine, int charsPerLine,
                                          boolean enableChineseOptimization) {
        this.maxHeaderWidth = maxHeaderWidth;
        this.minHeaderWidth = minHeaderWidth;
        this.headerExtraWidth = headerExtraWidth;
        this.maxRowHeight = maxRowHeight;
        this.minRowHeight = minRowHeight;
        this.headerRowHeight = headerRowHeight;
        this.defaultRowHeight = defaultRowHeight;
        this.heightPerLine = heightPerLine;
        this.charsPerLine = charsPerLine;
        this.enableChineseOptimization = enableChineseOptimization;
    }

    // ==================== WorkbookWriteHandler 方法 ====================

    @Override
    public void afterWorkbookDispose(WriteWorkbookHolder writeWorkbookHolder) {
        // 在工作簿处理完成后，为所有单元格设置自动换行样式
        Workbook workbook = writeWorkbookHolder.getWorkbook();
        for (int i = 0; i < workbook.getNumberOfSheets(); i++) {
            Sheet sheet = workbook.getSheetAt(i);
            setAutoWrapForAllCells(sheet, workbook);
        }
    }

    // ==================== RowWriteHandler 方法 ====================

    @Override
    public void afterRowCreate(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, 
                              Row row, Integer relativeRowIndex, Boolean isHead) {
        if (isHead) {
            // 表头行使用固定高度
            row.setHeightInPoints(headerRowHeight);
        } else {
            // 数据行根据内容调整高度
            float calculatedHeight = calculateRowHeight(row);
            float finalHeight = Math.max(minRowHeight, Math.min(calculatedHeight, maxRowHeight));
            row.setHeightInPoints(finalHeight);
        }
    }

    @Override
    public void afterRowDispose(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, 
                               Row row, Integer relativeRowIndex, Boolean isHead) {
        // 在表头行处理完成后调整表头列宽
        if (isHead && relativeRowIndex != null && relativeRowIndex == 0 && !headerProcessed.getAndSet(true)) {
            adjustHeaderColumnWidth(writeSheetHolder.getSheet(), row);
        }
    }

    // ==================== 私有方法 ====================

    /**
     * 调整表头列宽
     */
    private void adjustHeaderColumnWidth(Sheet sheet, Row headerRow) {
        if (headerRow == null) {
            return;
        }

        // 遍历表头的每一列
        int lastCellNum = headerRow.getLastCellNum();
        for (int columnIndex = 0; columnIndex < lastCellNum; columnIndex++) {
            Cell headerCell = headerRow.getCell(columnIndex);
            if (headerCell != null) {
                adjustHeaderCellWidth(sheet, columnIndex, headerCell);
            }
        }
    }

    /**
     * 调整单个表头单元格的列宽
     */
    private void adjustHeaderCellWidth(Sheet sheet, int columnIndex, Cell headerCell) {
        String headerText = getCellValueAsString(headerCell);
        if (headerText == null || headerText.isEmpty()) {
            return;
        }

        // 计算表头文字所需的宽度
        int headerTextWidth = calculateStringWidth(headerText) + headerExtraWidth;
        int finalWidth = Math.max(minHeaderWidth, Math.min(headerTextWidth, maxHeaderWidth));

        // 设置列宽（POI中的宽度单位是1/256字符宽度）
        sheet.setColumnWidth(columnIndex, finalWidth * 256);
    }

    /**
     * 计算行高
     */
    private float calculateRowHeight(Row row) {
        float maxHeight = defaultRowHeight;

        // 遍历行中的所有单元格
        for (int cellIndex = 0; cellIndex < row.getLastCellNum(); cellIndex++) {
            Cell cell = row.getCell(cellIndex);
            if (cell != null) {
                String cellValue = getCellValueAsString(cell);
                if (cellValue != null && !cellValue.isEmpty()) {
                    float cellHeight = calculateCellHeight(cellValue);
                    maxHeight = Math.max(maxHeight, cellHeight);
                }
            }
        }

        return maxHeight;
    }

    /**
     * 计算单元格内容所需的行高
     */
    private float calculateCellHeight(String content) {
        if (content == null || content.isEmpty()) {
            return defaultRowHeight;
        }

        // 计算换行数
        int lineCount = 1;

        // 按\n分割处理显式换行
        String[] lines = content.split("\n");
        lineCount = lines.length;

        // 计算每行因长度产生的额外换行
        for (String line : lines) {
            int lineWidth = calculateStringWidth(line);
            if (lineWidth > charsPerLine) {
                // 计算这一行需要多少行来显示
                int additionalLines = (lineWidth - 1) / charsPerLine;
                lineCount += additionalLines;
            }
        }

        // 根据行数计算高度
        return Math.max(defaultRowHeight, lineCount * heightPerLine);
    }

    /**
     * 为所有单元格设置自动换行样式
     */
    private void setAutoWrapForAllCells(Sheet sheet, Workbook workbook) {
        // 创建自动换行的单元格样式
        CellStyle wrapStyle = workbook.createCellStyle();
        wrapStyle.setWrapText(true);
        wrapStyle.setVerticalAlignment(VerticalAlignment.TOP);

        // 遍历所有行和单元格
        for (int rowIndex = 0; rowIndex <= sheet.getLastRowNum(); rowIndex++) {
            Row row = sheet.getRow(rowIndex);
            if (row != null) {
                for (int cellIndex = 0; cellIndex < row.getLastCellNum(); cellIndex++) {
                    Cell cell = row.getCell(cellIndex);
                    if (cell != null) {
                        // 为数据行设置自动换行（表头行不设置）
                        if (rowIndex > 0) {
                            cell.setCellStyle(wrapStyle);
                        }
                    }
                }
            }
        }
    }

    /**
     * 计算字符串显示宽度
     */
    private int calculateStringWidth(String str) {
        if (str == null || str.isEmpty()) {
            return 0;
        }

        if (!enableChineseOptimization) {
            return str.length();
        }

        int width = 0;
        for (char c : str.toCharArray()) {
            if (isChineseCharacter(c)) {
                // 中文字符占2个字符宽度
                width += 2;
            } else {
                // 英文字符占1个字符宽度
                width += 1;
            }
        }

        return width;
    }

    /**
     * 判断是否为中文字符
     */
    private boolean isChineseCharacter(char c) {
        // 中文字符的Unicode范围
        return (c >= 0x4E00 && c <= 0x9FFF) ||  // 基本汉字
               (c >= 0x3400 && c <= 0x4DBF) ||  // 扩展A
               (c >= 0x20000 && c <= 0x2A6DF) || // 扩展B
               (c >= 0x2A700 && c <= 0x2B73F) || // 扩展C
               (c >= 0x2B740 && c <= 0x2B81F) || // 扩展D
               (c >= 0x2B820 && c <= 0x2CEAF) || // 扩展E
               (c >= 0x2CEB0 && c <= 0x2EBEF) || // 扩展F
               (c >= 0x30000 && c <= 0x3134F) || // 扩展G
               (c >= 0xFF00 && c <= 0xFFEF);     // 全角字符
    }

    /**
     * 获取单元格值的字符串表示
     */
    private String getCellValueAsString(Cell cell) {
        if (cell == null) {
            return null;
        }

        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue().toString();
                } else {
                    return String.valueOf(cell.getNumericCellValue());
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                return cell.getCellFormula();
            default:
                return "";
        }
    }

    // ==================== 静态工厂方法 ====================

    /**
     * 创建默认配置的处理器
     */
    public static HeaderWidthContentHeightHandler create() {
        return new HeaderWidthContentHeightHandler();
    }

    /**
     * 创建自定义表头宽度配置的处理器
     */
    public static HeaderWidthContentHeightHandler createWithHeaderConfig(int maxHeaderWidth, int minHeaderWidth) {
        return new HeaderWidthContentHeightHandler(maxHeaderWidth, minHeaderWidth, HEADER_EXTRA_WIDTH,
                MAX_ROW_HEIGHT, MIN_ROW_HEIGHT, HEADER_ROW_HEIGHT, DEFAULT_ROW_HEIGHT,
                HEIGHT_PER_LINE, CHARS_PER_LINE, true);
    }

    /**
     * 创建自定义行高配置的处理器
     */
    public static HeaderWidthContentHeightHandler createWithRowHeightConfig(float maxRowHeight, float minRowHeight) {
        return new HeaderWidthContentHeightHandler(DEFAULT_MAX_HEADER_WIDTH, DEFAULT_MIN_HEADER_WIDTH, HEADER_EXTRA_WIDTH,
                maxRowHeight, minRowHeight, HEADER_ROW_HEIGHT, DEFAULT_ROW_HEIGHT,
                HEIGHT_PER_LINE, CHARS_PER_LINE, true);
    }
}
