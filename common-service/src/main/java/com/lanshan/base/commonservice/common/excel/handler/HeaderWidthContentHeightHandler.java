package com.lanshan.base.commonservice.common.excel.handler;

import com.alibaba.excel.write.handler.RowWriteHandler;
import com.alibaba.excel.write.handler.WorkbookWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteTableHolder;
import com.alibaba.excel.write.metadata.holder.WriteWorkbookHolder;
import org.apache.poi.ss.usermodel.*;

import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 智能列宽自适应 + 内容高度自适应处理器
 *
 * 功能特点：
 * 1. 智能列宽决策：比较表头和内容宽度，取较长者作为列宽（不超过最大值）
 * 2. 内容高度自适应：超过列宽的内容通过增加行高来显示
 * 3. 自动换行：超过列宽的内容（表头或数据）都自动换行显示
 * 4. 中文字符优化：中文字符按2个字符宽度计算
 *
 * 列宽决策逻辑：
 * - 如果内容比表头长且未达到最大值，使用内容宽度
 * - 如果表头比内容长且未达到最大值，使用表头宽度
 * - 如果都超过最大值，使用最大值，超出部分换行显示
 * - 如果都小于最小值，使用最小值
 *
 * <AUTHOR>
 * @version 2.0.0
 * @date 2025/7/6
 */
public class HeaderWidthContentHeightHandler implements WorkbookWriteHandler, RowWriteHandler {

    /**
     * 默认最大列宽（字符数）
     */
    private static final int DEFAULT_MAX_COLUMN_WIDTH = 30;

    /**
     * 默认最小列宽（字符数）
     */
    private static final int DEFAULT_MIN_COLUMN_WIDTH = 8;

    /**
     * 列额外宽度（字符数）- 为了美观留出的额外空间
     */
    private static final int COLUMN_EXTRA_WIDTH = 2;

    /**
     * 默认行高（磅）
     */
    private static final float DEFAULT_ROW_HEIGHT = 15f;

    /**
     * 最大行高（磅）
     */
    private static final float MAX_ROW_HEIGHT = 120f;

    /**
     * 最小行高（磅）
     */
    private static final float MIN_ROW_HEIGHT = 12f;

    /**
     * 标题行高（磅）
     */
    private static final float HEADER_ROW_HEIGHT = 25f;

    /**
     * 每行文字对应的行高增量（磅）
     */
    private static final float HEIGHT_PER_LINE = 16f;

    /**
     * 每行字符数阈值（超过此数量会换行）
     */
    private static final int CHARS_PER_LINE = 30;

    // 配置参数
    private final int maxColumnWidth;
    private final int minColumnWidth;
    private final int columnExtraWidth;
    private final float maxRowHeight;
    private final float minRowHeight;
    private final float headerRowHeight;
    private final float defaultRowHeight;
    private final float heightPerLine;
    private final int charsPerLine;
    private final boolean enableChineseOptimization;

    /**
     * 标记是否已经处理过列宽
     */
    private final AtomicBoolean columnWidthProcessed = new AtomicBoolean(false);

    /**
     * 存储每列的最大内容宽度（用于与表头宽度比较）
     */
    private final java.util.Map<Integer, Integer> columnMaxContentWidth = new java.util.concurrent.ConcurrentHashMap<>();

    /**
     * 存储每列的表头宽度
     */
    private final java.util.Map<Integer, Integer> columnHeaderWidth = new java.util.concurrent.ConcurrentHashMap<>();

    /**
     * 默认构造函数
     */
    public HeaderWidthContentHeightHandler() {
        this(DEFAULT_MAX_COLUMN_WIDTH, DEFAULT_MIN_COLUMN_WIDTH, COLUMN_EXTRA_WIDTH,
             MAX_ROW_HEIGHT, MIN_ROW_HEIGHT, HEADER_ROW_HEIGHT, DEFAULT_ROW_HEIGHT,
             HEIGHT_PER_LINE, CHARS_PER_LINE, true);
    }

    /**
     * 自定义构造函数
     */
    public HeaderWidthContentHeightHandler(int maxColumnWidth, int minColumnWidth, int columnExtraWidth,
                                          float maxRowHeight, float minRowHeight, float headerRowHeight,
                                          float defaultRowHeight, float heightPerLine, int charsPerLine,
                                          boolean enableChineseOptimization) {
        this.maxColumnWidth = maxColumnWidth;
        this.minColumnWidth = minColumnWidth;
        this.columnExtraWidth = columnExtraWidth;
        this.maxRowHeight = maxRowHeight;
        this.minRowHeight = minRowHeight;
        this.headerRowHeight = headerRowHeight;
        this.defaultRowHeight = defaultRowHeight;
        this.heightPerLine = heightPerLine;
        this.charsPerLine = charsPerLine;
        this.enableChineseOptimization = enableChineseOptimization;
    }

    // ==================== WorkbookWriteHandler 方法 ====================

    @Override
    public void afterWorkbookDispose(WriteWorkbookHolder writeWorkbookHolder) {
        // 在工作簿处理完成后，调整列宽并为所有单元格设置自动换行样式
        Workbook workbook = writeWorkbookHolder.getWorkbook();
        for (int i = 0; i < workbook.getNumberOfSheets(); i++) {
            Sheet sheet = workbook.getSheetAt(i);
            // 先调整列宽，再设置自动换行
            adjustColumnWidthBasedOnAllContent(sheet);
            setAutoWrapForAllCells(sheet, workbook);
        }
    }

    // ==================== RowWriteHandler 方法 ====================

    @Override
    public void afterRowCreate(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, 
                              Row row, Integer relativeRowIndex, Boolean isHead) {
        if (isHead) {
            // 表头行使用固定高度
            row.setHeightInPoints(headerRowHeight);
        } else {
            // 数据行根据内容调整高度
            float calculatedHeight = calculateRowHeight(row);
            float finalHeight = Math.max(minRowHeight, Math.min(calculatedHeight, maxRowHeight));
            row.setHeightInPoints(finalHeight);
        }
    }

    @Override
    public void afterRowDispose(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder,
                               Row row, Integer relativeRowIndex, Boolean isHead) {
        // 收集每行的内容宽度信息，但不立即调整列宽
        if (row != null) {
            collectRowContentWidth(row);
        }
    }

    // ==================== 私有方法 ====================

    /**
     * 收集行内容宽度信息
     */
    private void collectRowContentWidth(Row row) {
        if (row == null) {
            return;
        }

        boolean isHeaderRow = row.getRowNum() == 0;

        // 遍历行中的所有单元格
        int lastCellNum = row.getLastCellNum();
        for (int columnIndex = 0; columnIndex < lastCellNum; columnIndex++) {
            Cell cell = row.getCell(columnIndex);
            if (cell != null) {
                String cellValue = getCellValueAsString(cell);
                if (cellValue != null && !cellValue.isEmpty()) {
                    int contentWidth = calculateStringWidth(cellValue);

                    if (isHeaderRow) {
                        // 如果是表头行，存储表头宽度
                        columnHeaderWidth.put(columnIndex, contentWidth);
                    } else {
                        // 如果是数据行，更新该列的最大内容宽度
                        columnMaxContentWidth.merge(columnIndex, contentWidth, Integer::max);
                    }
                }
            }
        }
    }

    /**
     * 基于所有内容（表头+数据）调整列宽
     *
     * 决策逻辑：
     * 1. 比较表头宽度和内容最大宽度，取较长者
     * 2. 如果都没超过最大值，使用较长者的宽度
     * 3. 如果超过最大值，则使用最大值作为列宽
     * 4. 超过列宽的内容通过换行和增加行高来显示
     */
    private void adjustColumnWidthBasedOnAllContent(Sheet sheet) {
        if (sheet.getLastRowNum() < 0) {
            return;
        }

        Row headerRow = sheet.getRow(0);
        if (headerRow == null) {
            return;
        }

        // 遍历每一列
        int lastCellNum = headerRow.getLastCellNum();
        for (int columnIndex = 0; columnIndex < lastCellNum; columnIndex++) {
            adjustSingleColumnWidth(sheet, headerRow, columnIndex);
        }
    }

    /**
     * 调整单个列的宽度
     */
    private void adjustSingleColumnWidth(Sheet sheet, Row headerRow, int columnIndex) {
        // 获取表头宽度（从存储的Map中获取）
        int headerWidth = columnHeaderWidth.getOrDefault(columnIndex, 0);

        // 获取该列内容的最大宽度
        int maxContentWidth = columnMaxContentWidth.getOrDefault(columnIndex, 0);

        // 智能决策列宽：取表头和内容中较长者，但不超过最大值
        int targetWidth = Math.max(headerWidth, maxContentWidth) + columnExtraWidth;
        int finalWidth = Math.max(minColumnWidth, Math.min(targetWidth, maxColumnWidth));

        // 设置列宽（POI中的宽度单位是1/256字符宽度）
        sheet.setColumnWidth(columnIndex, finalWidth * 256);
    }

    /**
     * 计算行高（基于实际列宽）
     */
    private float calculateRowHeight(Row row) {
        float maxHeight = defaultRowHeight;

        // 遍历行中的所有单元格
        for (int cellIndex = 0; cellIndex < row.getLastCellNum(); cellIndex++) {
            Cell cell = row.getCell(cellIndex);
            if (cell != null) {
                String cellValue = getCellValueAsString(cell);
                if (cellValue != null && !cellValue.isEmpty()) {
                    // 获取该列的实际宽度限制
                    int columnWidthLimit = getColumnWidthLimit(cellIndex);
                    float cellHeight = calculateCellHeightWithColumnWidth(cellValue, columnWidthLimit);
                    maxHeight = Math.max(maxHeight, cellHeight);
                }
            }
        }

        return maxHeight;
    }

    /**
     * 获取列宽限制（字符数）
     */
    private int getColumnWidthLimit(int columnIndex) {
        // 获取表头宽度（从存储的Map中获取）
        int headerWidth = columnHeaderWidth.getOrDefault(columnIndex, 0);

        // 获取该列内容的最大宽度
        int maxContentWidth = columnMaxContentWidth.getOrDefault(columnIndex, 0);

        // 使用相同的决策逻辑计算列宽限制
        int targetWidth = Math.max(headerWidth, maxContentWidth) + columnExtraWidth;
        return Math.max(minColumnWidth, Math.min(targetWidth, maxColumnWidth));
    }

    /**
     * 计算单元格内容所需的行高（基于列宽限制）
     */
    private float calculateCellHeightWithColumnWidth(String content, int columnWidthLimit) {
        if (content == null || content.isEmpty()) {
            return defaultRowHeight;
        }

        // 计算换行数
        int lineCount = 1;

        // 按\n分割处理显式换行
        String[] lines = content.split("\n");
        lineCount = lines.length;

        // 计算每行因长度产生的额外换行（基于实际列宽）
        for (String line : lines) {
            int lineWidth = calculateStringWidth(line);
            if (lineWidth > columnWidthLimit) {
                // 计算这一行需要多少行来显示（基于实际列宽）
                int additionalLines = (lineWidth - 1) / columnWidthLimit;
                lineCount += additionalLines;
            }
        }

        // 根据行数计算高度
        return Math.max(defaultRowHeight, lineCount * heightPerLine);
    }

    /**
     * 计算单元格内容所需的行高（兼容旧方法）
     */
    private float calculateCellHeight(String content) {
        return calculateCellHeightWithColumnWidth(content, charsPerLine);
    }

    /**
     * 为所有单元格设置自动换行样式
     */
    private void setAutoWrapForAllCells(Sheet sheet, Workbook workbook) {
        // 创建自动换行的单元格样式
        CellStyle wrapStyle = workbook.createCellStyle();
        wrapStyle.setWrapText(true);
        wrapStyle.setVerticalAlignment(VerticalAlignment.TOP);

        // 遍历所有行和单元格
        for (int rowIndex = 0; rowIndex <= sheet.getLastRowNum(); rowIndex++) {
            Row row = sheet.getRow(rowIndex);
            if (row != null) {
                for (int cellIndex = 0; cellIndex < row.getLastCellNum(); cellIndex++) {
                    Cell cell = row.getCell(cellIndex);
                    if (cell != null) {
                        // 为数据行设置自动换行（表头行不设置）
                        if (rowIndex > 0) {
                            cell.setCellStyle(wrapStyle);
                        }
                    }
                }
            }
        }
    }

    /**
     * 计算字符串显示宽度
     */
    private int calculateStringWidth(String str) {
        if (str == null || str.isEmpty()) {
            return 0;
        }

        if (!enableChineseOptimization) {
            return str.length();
        }

        int width = 0;
        for (char c : str.toCharArray()) {
            if (isChineseCharacter(c)) {
                // 中文字符占2个字符宽度
                width += 2;
            } else {
                // 英文字符占1个字符宽度
                width += 1;
            }
        }

        return width;
    }

    /**
     * 判断是否为中文字符
     */
    private boolean isChineseCharacter(char c) {
        // 中文字符的Unicode范围
        return (c >= 0x4E00 && c <= 0x9FFF) ||  // 基本汉字
               (c >= 0x3400 && c <= 0x4DBF) ||  // 扩展A
               (c >= 0x20000 && c <= 0x2A6DF) || // 扩展B
               (c >= 0x2A700 && c <= 0x2B73F) || // 扩展C
               (c >= 0x2B740 && c <= 0x2B81F) || // 扩展D
               (c >= 0x2B820 && c <= 0x2CEAF) || // 扩展E
               (c >= 0x2CEB0 && c <= 0x2EBEF) || // 扩展F
               (c >= 0x30000 && c <= 0x3134F) || // 扩展G
               (c >= 0xFF00 && c <= 0xFFEF);     // 全角字符
    }

    /**
     * 获取单元格值的字符串表示
     */
    private String getCellValueAsString(Cell cell) {
        if (cell == null) {
            return null;
        }

        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue().toString();
                } else {
                    return String.valueOf(cell.getNumericCellValue());
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                return cell.getCellFormula();
            default:
                return "";
        }
    }

    // ==================== 静态工厂方法 ====================

    /**
     * 创建默认配置的处理器
     */
    public static HeaderWidthContentHeightHandler create() {
        return new HeaderWidthContentHeightHandler();
    }

    /**
     * 创建自定义列宽配置的处理器
     */
    public static HeaderWidthContentHeightHandler createWithColumnConfig(int maxColumnWidth, int minColumnWidth) {
        return new HeaderWidthContentHeightHandler(maxColumnWidth, minColumnWidth, COLUMN_EXTRA_WIDTH,
                MAX_ROW_HEIGHT, MIN_ROW_HEIGHT, HEADER_ROW_HEIGHT, DEFAULT_ROW_HEIGHT,
                HEIGHT_PER_LINE, CHARS_PER_LINE, true);
    }

    /**
     * 创建自定义行高配置的处理器
     */
    public static HeaderWidthContentHeightHandler createWithRowHeightConfig(float maxRowHeight, float minRowHeight) {
        return new HeaderWidthContentHeightHandler(DEFAULT_MAX_COLUMN_WIDTH, DEFAULT_MIN_COLUMN_WIDTH, COLUMN_EXTRA_WIDTH,
                maxRowHeight, minRowHeight, HEADER_ROW_HEIGHT, DEFAULT_ROW_HEIGHT,
                HEIGHT_PER_LINE, CHARS_PER_LINE, true);
    }
}
