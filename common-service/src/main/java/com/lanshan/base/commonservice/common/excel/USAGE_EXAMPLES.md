# Excel导出Handler使用示例

## 🎯 推荐使用方式

### 1. 智能Handler选择（推荐）

```java
@GetMapping("/export")
public void exportData(HttpServletResponse response) throws IOException {
    List<DataDTO> dataList = getExportData();
    
    // 设置响应头
    response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
    response.setCharacterEncoding("utf-8");
    String fileName = URLEncoder.encode("数据导出", "UTF-8").replaceAll("\\+", "%20");
    response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");

    // 🚀 智能选择最优Handler
    Object handler = ExcelExportStrategy.createOptimalHandler(dataList.size());
    
    // 构建Excel写入器
    var writerBuilder = EasyExcel.write(response.getOutputStream(), DataDTO.class);
    if (handler != null) {
        writerBuilder.registerWriteHandler(handler);
    }
    
    // 执行导出
    writerBuilder.sheet("数据表").doWrite(dataList);
}
```

### 2. 手动指定Handler类型

```java
// 小数据量 - 完整优化
Object handler = ExcelExportStrategy.createSmallDataHandler();

// 中等数据量 - 采样优化
Object handler = ExcelExportStrategy.createMediumDataHandler();

// 大数据量 - 简单优化
Object handler = ExcelExportStrategy.createLargeDataHandler();

// 超大数据量 - 不使用Handler
Object handler = null;
```

### 3. 直接创建Handler

```java
// 完整功能Handler
HeaderWidthContentHeightHandler handler = new HeaderWidthContentHeightHandler();

// 大数据量优化Handler
HeaderWidthContentHeightHandler handler = HeaderWidthContentHeightHandler.createForLargeData(1000);

// 超大数据量Handler
HeaderWidthContentHeightHandler handler = HeaderWidthContentHeightHandler.createForMassiveData();

// 简单固定列宽Handler
SimpleColumnWidthHandler handler = SimpleColumnWidthHandler.create(18);
```

## 📊 策略选择逻辑

| 数据量 | 自动选择的Handler | 特点 | 性能影响 |
|-------|------------------|------|---------|
| < 1,000行 | `HeaderWidthContentHeightHandler` | 完整优化，最佳美观 | *****% |
| 1,000-10,000行 | `HeaderWidthContentHeightHandler.createForLargeData(1000)` | 采样优化，平衡性能与美观 | +10-20% |
| 10,000-50,000行 | `SimpleColumnWidthHandler.create(18)` | 固定列宽，性能优先 | +1-3% |
| > 50,000行 | `null` | 无特殊处理，最快速度 | 0% |

## 🔧 实际应用场景

### 场景1：用户数据导出（常规）
```java
@GetMapping("/exportUsers")
public void exportUsers(HttpServletResponse response) throws IOException {
    List<UserExportDTO> users = userService.getAllUsers();
    
    response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
    response.setCharacterEncoding("utf-8");
    String fileName = URLEncoder.encode("用户数据", "UTF-8").replaceAll("\\+", "%20");
    response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");

    // 智能选择Handler
    Object handler = ExcelExportStrategy.createOptimalHandler(users.size());
    
    var writerBuilder = EasyExcel.write(response.getOutputStream(), UserExportDTO.class);
    if (handler != null) {
        writerBuilder.registerWriteHandler(handler);
    }
    
    writerBuilder.sheet("用户数据").doWrite(users);
}
```

### 场景2：报表导出（追求美观）
```java
@GetMapping("/exportReport")
public void exportReport(HttpServletResponse response) throws IOException {
    List<ReportDTO> reportData = reportService.getReportData();
    
    response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
    response.setCharacterEncoding("utf-8");
    String fileName = URLEncoder.encode("统计报表", "UTF-8").replaceAll("\\+", "%20");
    response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");

    // 报表场景强制使用完整优化，确保美观
    HeaderWidthContentHeightHandler handler = ExcelExportStrategy.createSmallDataHandler();
    
    EasyExcel.write(response.getOutputStream(), ReportDTO.class)
            .registerWriteHandler(handler)
            .sheet("统计报表")
            .doWrite(reportData);
}
```

### 场景3：大批量数据导出（性能优先）
```java
@GetMapping("/exportLargeData")
public void exportLargeData(HttpServletResponse response) throws IOException {
    List<LargeDataDTO> largeData = dataService.getLargeDataSet();
    
    response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
    response.setCharacterEncoding("utf-8");
    String fileName = URLEncoder.encode("大批量数据", "UTF-8").replaceAll("\\+", "%20");
    response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");

    // 大数据量场景使用简单Handler
    SimpleColumnWidthHandler handler = ExcelExportStrategy.createLargeDataHandler();
    
    EasyExcel.write(response.getOutputStream(), LargeDataDTO.class)
            .registerWriteHandler(handler)
            .sheet("数据表")
            .doWrite(largeData);
}
```

### 场景4：超大数据量导出（最小开销）
```java
@GetMapping("/exportMassiveData")
public void exportMassiveData(HttpServletResponse response) throws IOException {
    List<MassiveDataDTO> massiveData = dataService.getMassiveDataSet();
    
    response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
    response.setCharacterEncoding("utf-8");
    String fileName = URLEncoder.encode("超大数据集", "UTF-8").replaceAll("\\+", "%20");
    response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");

    // 超大数据量不使用任何Handler，追求最快速度
    EasyExcel.write(response.getOutputStream(), MassiveDataDTO.class)
            .sheet("数据表")
            .doWrite(massiveData);
}
```

## 🎨 自定义配置示例

### 自定义列宽配置
```java
// 创建自定义列宽的Handler
HeaderWidthContentHeightHandler handler = HeaderWidthContentHeightHandler.createWithColumnConfig(25, 10);

// 或者完全自定义
HeaderWidthContentHeightHandler handler = new HeaderWidthContentHeightHandler(
    25,    // 最大列宽
    10,    // 最小列宽
    1,     // 额外宽度
    80f,   // 最大行高
    12f,   // 最小行高
    20f,   // 表头行高
    15f,   // 默认行高
    15f,   // 每行高度
    20,    // 每行字符数阈值
    true,  // 启用中文优化
    0      // 不采样
);
```

### 自定义简单Handler
```java
// 创建不同列宽的简单Handler
SimpleColumnWidthHandler handler1 = SimpleColumnWidthHandler.create(15); // 窄列
SimpleColumnWidthHandler handler2 = SimpleColumnWidthHandler.create(20); // 中等列
SimpleColumnWidthHandler handler3 = SimpleColumnWidthHandler.create(25); // 宽列
```

## 📋 最佳实践建议

### 1. 通用导出方法
```java
/**
 * 通用Excel导出方法
 */
public static <T> void exportExcel(HttpServletResponse response, Class<T> clazz, 
                                  List<T> dataList, String fileName, String sheetName) throws IOException {
    response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
    response.setCharacterEncoding("utf-8");
    String encodedFileName = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
    response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + encodedFileName + ".xlsx");

    // 智能选择Handler
    Object handler = ExcelExportStrategy.createOptimalHandler(dataList.size());
    
    var writerBuilder = EasyExcel.write(response.getOutputStream(), clazz);
    if (handler != null) {
        writerBuilder.registerWriteHandler(handler);
    }
    
    writerBuilder.sheet(sheetName).doWrite(dataList);
}
```

### 2. 使用示例
```java
// 调用通用方法
exportExcel(response, NewStudentExportDTO.class, exportDTOList, "新生信息表", "新生信息表");
```

### 3. 性能监控
```java
@GetMapping("/export")
public void exportData(HttpServletResponse response) throws IOException {
    long startTime = System.currentTimeMillis();
    List<DataDTO> dataList = getExportData();
    
    // 打印策略信息（开发环境）
    if (log.isDebugEnabled()) {
        ExcelExportStrategy.printStrategyAdvice(dataList.size());
    }
    
    Object handler = ExcelExportStrategy.createOptimalHandler(dataList.size());
    
    // ... 执行导出 ...
    
    long endTime = System.currentTimeMillis();
    log.info("Excel导出完成，数据量: {}, 耗时: {}ms", dataList.size(), endTime - startTime);
}
```

## 🎯 总结

1. **推荐使用 `ExcelExportStrategy.createOptimalHandler()`** - 自动选择最优Handler
2. **保持原生EasyExcel写法** - 只是在registerWriteHandler时使用策略选择
3. **根据场景选择** - 报表用完整优化，批量导出用性能优化
4. **监控性能** - 记录导出耗时，根据实际情况调整策略
