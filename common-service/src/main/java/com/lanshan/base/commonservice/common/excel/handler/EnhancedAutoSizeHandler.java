package com.lanshan.base.commonservice.common.excel.handler;

import com.alibaba.excel.write.handler.RowWriteHandler;
import com.alibaba.excel.write.handler.WorkbookWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteTableHolder;
import com.alibaba.excel.write.metadata.holder.WriteWorkbookHolder;
import com.lanshan.base.commonservice.common.excel.util.EasyExcelAutoSizeUtil;
import org.apache.poi.ss.usermodel.*;

import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 增强的自适应大小处理器
 * <p>
 * 集成了列宽、行高和表头宽度的自适应功能
 * 避免多个处理器之间的冲突
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/7/6
 */
public class EnhancedAutoSizeHandler implements WorkbookWriteHandler, RowWriteHandler {

    /**
     * 列宽配置
     */
    private final AutoSizeHandler.ColumnWidthConfig columnConfig;

    /**
     * 行高配置
     */
    private final AutoSizeHandler.RowHeightConfig rowConfig;

    /**
     * 表头宽度配置
     */
    private final EasyExcelAutoSizeUtil.HeaderWidthConfig headerConfig;

    /**
     * 列宽处理器
     */
    private final AutoColumnWidthHandler columnWidthHandler;

    /**
     * 行高处理器
     */
    private final AutoRowHeightHandler rowHeightHandler;

    /**
     * 标记是否已经处理过表头
     */
    private final AtomicBoolean headerProcessed = new AtomicBoolean(false);

    /**
     * 构造函数
     *
     * @param columnConfig 列宽配置
     * @param rowConfig    行高配置
     * @param headerConfig 表头宽度配置
     */
    public EnhancedAutoSizeHandler(AutoSizeHandler.ColumnWidthConfig columnConfig,
                                   AutoSizeHandler.RowHeightConfig rowConfig,
                                   EasyExcelAutoSizeUtil.HeaderWidthConfig headerConfig) {
        this.columnConfig = columnConfig;
        this.rowConfig = rowConfig;
        this.headerConfig = headerConfig;

        this.columnWidthHandler = new AutoColumnWidthHandler(
                columnConfig.isEnableChineseOptimization(),
                columnConfig.getMaxColumnWidth(),
                columnConfig.getMinColumnWidth()
        );

        this.rowHeightHandler = new AutoRowHeightHandler(
                rowConfig.getMaxRowHeight(),
                rowConfig.getMinRowHeight(),
                rowConfig.getHeaderRowHeight(),
                rowConfig.getDefaultRowHeight(),
                rowConfig.getHeightPerLine(),
                rowConfig.isEnableAutoWrap()
        );
    }

    // ==================== WorkbookWriteHandler 方法 ====================

    @Override
    public void afterWorkbookDispose(WriteWorkbookHolder writeWorkbookHolder) {
        // 只有在不是仅调整表头宽度时，才处理数据列宽
        if (!headerConfig.isHeaderOnly()) {
            columnWidthHandler.afterWorkbookDispose(writeWorkbookHolder);
        }
    }

    // ==================== RowWriteHandler 方法 ====================

    @Override
    public void afterRowCreate(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, Row row, Integer relativeRowIndex, Boolean isHead) {
        // 委托给行高处理器
        rowHeightHandler.afterRowCreate(writeSheetHolder, writeTableHolder, row, relativeRowIndex, isHead);
    }

    @Override
    public void afterRowDispose(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, Row row, Integer relativeRowIndex, Boolean isHead) {
        // 在表头行处理完成后调整表头宽度
        if (isHead && relativeRowIndex != null && relativeRowIndex == 0 && !headerProcessed.getAndSet(true)) {
            adjustHeaderColumnWidth(writeSheetHolder.getSheet(), row);
        }
    }

    // ==================== 表头宽度处理方法 ====================

    /**
     * 调整表头列宽
     *
     * @param sheet     工作表
     * @param headerRow 表头行
     */
    private void adjustHeaderColumnWidth(Sheet sheet, Row headerRow) {
        if (headerRow == null) {
            return;
        }

        // 遍历表头的每一列
        int lastCellNum = headerRow.getLastCellNum();
        for (int columnIndex = 0; columnIndex < lastCellNum; columnIndex++) {
            Cell headerCell = headerRow.getCell(columnIndex);
            if (headerCell != null) {
                adjustHeaderCellWidth(sheet, columnIndex, headerCell);
            }
        }
    }

    /**
     * 调整单个表头单元格的列宽
     *
     * @param sheet       工作表
     * @param columnIndex 列索引
     * @param headerCell  表头单元格
     */
    private void adjustHeaderCellWidth(Sheet sheet, int columnIndex, Cell headerCell) {
        String headerText = getCellValueAsString(headerCell);
        if (headerText == null || headerText.isEmpty()) {
            return;
        }

        // 计算表头文字所需的宽度
        int headerTextWidth = calculateStringWidth(headerText) + headerConfig.getHeaderExtraWidth();

        // 如果只调整表头宽度，直接设置
        if (headerConfig.isHeaderOnly()) {
            int finalWidth = Math.max(headerConfig.getMinHeaderWidth(), 
                                    Math.min(headerTextWidth, headerConfig.getMaxHeaderWidth()));
            sheet.setColumnWidth(columnIndex, finalWidth * 256);
            return;
        }

        // 如果需要考虑数据列宽，获取当前列宽
        int currentWidth = sheet.getColumnWidth(columnIndex) / 256;
        int finalWidth = Math.max(currentWidth, headerTextWidth);
        finalWidth = Math.max(headerConfig.getMinHeaderWidth(), 
                            Math.min(finalWidth, headerConfig.getMaxHeaderWidth()));

        sheet.setColumnWidth(columnIndex, finalWidth * 256);
    }

    /**
     * 获取单元格值的字符串表示
     *
     * @param cell 单元格
     * @return 字符串值
     */
    private String getCellValueAsString(Cell cell) {
        if (cell == null) {
            return null;
        }

        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue().toString();
                } else {
                    return String.valueOf(cell.getNumericCellValue());
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                return cell.getCellFormula();
            default:
                return "";
        }
    }

    /**
     * 计算字符串显示宽度
     *
     * @param str 字符串
     * @return 显示宽度
     */
    private int calculateStringWidth(String str) {
        if (str == null || str.isEmpty()) {
            return 0;
        }

        if (!headerConfig.isEnableChineseOptimization()) {
            return str.length();
        }

        int width = 0;
        for (char c : str.toCharArray()) {
            if (isChineseCharacter(c)) {
                // 中文字符占2个字符宽度
                width += 2;
            } else {
                // 英文字符占1个字符宽度
                width += 1;
            }
        }

        return width;
    }

    /**
     * 判断是否为中文字符
     *
     * @param c 字符
     * @return 是否为中文字符
     */
    private boolean isChineseCharacter(char c) {
        // 中文字符的Unicode范围
        return (c >= 0x4E00 && c <= 0x9FFF) ||  // 基本汉字
                (c >= 0x3400 && c <= 0x4DBF) ||  // 扩展A
                (c >= 0x20000 && c <= 0x2A6DF) || // 扩展B
                (c >= 0x2A700 && c <= 0x2B73F) || // 扩展C
                (c >= 0x2B740 && c <= 0x2B81F) || // 扩展D
                (c >= 0x2B820 && c <= 0x2CEAF) || // 扩展E
                (c >= 0x2CEB0 && c <= 0x2EBEF) || // 扩展F
                (c >= 0x30000 && c <= 0x3134F) || // 扩展G
                (c >= 0x3400 && c <= 0x4DB5) ||   // 扩展A
                (c >= 0xFF00 && c <= 0xFFEF);     // 全角字符
    }

    /**
     * 创建默认的增强自适应处理器
     *
     * @return EnhancedAutoSizeHandler实例
     */
    public static EnhancedAutoSizeHandler create() {
        AutoSizeHandler.ColumnWidthConfig columnConfig = new AutoSizeHandler.ColumnWidthConfig();
        AutoSizeHandler.RowHeightConfig rowConfig = new AutoSizeHandler.RowHeightConfig();
        EasyExcelAutoSizeUtil.HeaderWidthConfig headerConfig = new EasyExcelAutoSizeUtil.HeaderWidthConfig();
        return new EnhancedAutoSizeHandler(columnConfig, rowConfig, headerConfig);
    }
}
