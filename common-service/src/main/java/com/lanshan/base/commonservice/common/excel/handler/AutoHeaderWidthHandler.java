package com.lanshan.base.commonservice.common.excel.handler;

import com.alibaba.excel.write.handler.RowWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteTableHolder;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.DateUtil;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;

import java.util.concurrent.atomic.AtomicBoolean;

/**
 * EasyExcel表头自适应宽度处理器
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/7/6
 */
public class AutoHeaderWidthHandler implements RowWriteHandler {

    /**
     * 默认最大表头列宽（字符数）
     */
    private static final int DEFAULT_MAX_HEADER_WIDTH = 30;

    /**
     * 默认最小表头列宽（字符数）
     */
    private static final int DEFAULT_MIN_HEADER_WIDTH = 8;

    /**
     * 表头额外宽度（字符数）- 为了美观留出的额外空间
     */
    private static final int HEADER_EXTRA_WIDTH = 2;

    /**
     * 最大表头列宽
     */
    private final int maxHeaderWidth;

    /**
     * 最小表头列宽
     */
    private final int minHeaderWidth;

    /**
     * 表头额外宽度
     */
    private final int headerExtraWidth;

    /**
     * 是否启用中文字符宽度优化
     */
    private final boolean enableChineseOptimization;

    /**
     * 是否只调整表头宽度（不影响数据列宽）
     */
    private final boolean headerOnly;

    /**
     * 标记是否已经处理过表头
     */
    private final AtomicBoolean headerProcessed = new AtomicBoolean(false);

    /**
     * 默认构造函数
     */
    public AutoHeaderWidthHandler() {
        this(DEFAULT_MAX_HEADER_WIDTH, DEFAULT_MIN_HEADER_WIDTH, HEADER_EXTRA_WIDTH, true, true);
    }

    /**
     * 自定义构造函数
     *
     * @param maxHeaderWidth            最大表头列宽
     * @param minHeaderWidth            最小表头列宽
     * @param headerExtraWidth          表头额外宽度
     * @param enableChineseOptimization 是否启用中文字符宽度优化
     * @param headerOnly                是否只调整表头宽度
     */
    public AutoHeaderWidthHandler(int maxHeaderWidth, int minHeaderWidth, int headerExtraWidth,
                                  boolean enableChineseOptimization, boolean headerOnly) {
        this.maxHeaderWidth = maxHeaderWidth;
        this.minHeaderWidth = minHeaderWidth;
        this.headerExtraWidth = headerExtraWidth;
        this.enableChineseOptimization = enableChineseOptimization;
        this.headerOnly = headerOnly;
    }

    @Override
    public void afterRowCreate(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, Row row, Integer relativeRowIndex, Boolean isHead) {
        // 暂时不在这里处理，等待表头内容完全写入
    }

    @Override
    public void afterRowDispose(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, Row row, Integer relativeRowIndex, Boolean isHead) {
        // 在表头行处理完成后调整宽度
        if (isHead && relativeRowIndex != null && relativeRowIndex == 0 && !headerProcessed.getAndSet(true)) {
            adjustHeaderColumnWidth(writeSheetHolder.getSheet(), row);
        }
    }

    /**
     * 调整表头列宽
     *
     * @param sheet     工作表
     * @param headerRow 表头行
     */
    private void adjustHeaderColumnWidth(Sheet sheet, Row headerRow) {
        if (headerRow == null) {
            return;
        }

        // 遍历表头的每一列
        int lastCellNum = headerRow.getLastCellNum();
        for (int columnIndex = 0; columnIndex < lastCellNum; columnIndex++) {
            Cell headerCell = headerRow.getCell(columnIndex);
            if (headerCell != null) {
                adjustHeaderCellWidth(sheet, columnIndex, headerCell);
            }
        }
    }

    /**
     * 调整单个表头单元格的列宽
     *
     * @param sheet       工作表
     * @param columnIndex 列索引
     * @param headerCell  表头单元格
     */
    private void adjustHeaderCellWidth(Sheet sheet, int columnIndex, Cell headerCell) {
        String headerText = getCellValueAsString(headerCell);
        if (headerText == null || headerText.isEmpty()) {
            return;
        }

        // 计算表头文字所需的宽度
        int headerTextWidth = calculateStringWidth(headerText) + headerExtraWidth;

        // 如果只调整表头宽度，直接设置
        if (headerOnly) {
            int finalWidth = Math.max(minHeaderWidth, Math.min(headerTextWidth, maxHeaderWidth));
            sheet.setColumnWidth(columnIndex, finalWidth * 256);
            return;
        }

        // 如果需要考虑数据列宽，获取当前列宽
        int currentWidth = sheet.getColumnWidth(columnIndex) / 256;
        int finalWidth = Math.max(currentWidth, headerTextWidth);
        finalWidth = Math.max(minHeaderWidth, Math.min(finalWidth, maxHeaderWidth));

        sheet.setColumnWidth(columnIndex, finalWidth * 256);
    }

    /**
     * 获取单元格值的字符串表示
     *
     * @param cell 单元格
     * @return 字符串值
     */
    private String getCellValueAsString(Cell cell) {
        if (cell == null) {
            return null;
        }

        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue().toString();
                } else {
                    return String.valueOf(cell.getNumericCellValue());
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                return cell.getCellFormula();
            default:
                return "";
        }
    }

    /**
     * 计算字符串显示宽度
     *
     * @param str 字符串
     * @return 显示宽度
     */
    private int calculateStringWidth(String str) {
        if (str == null || str.isEmpty()) {
            return 0;
        }

        if (!enableChineseOptimization) {
            return str.length();
        }

        int width = 0;
        for (char c : str.toCharArray()) {
            if (isChineseCharacter(c)) {
                // 中文字符占2个字符宽度
                width += 2;
            } else {
                // 英文字符占1个字符宽度
                width += 1;
            }
        }

        return width;
    }

    /**
     * 判断是否为中文字符
     *
     * @param c 字符
     * @return 是否为中文字符
     */
    private boolean isChineseCharacter(char c) {
        // 中文字符的Unicode范围
        return (c >= 0x4E00 && c <= 0x9FFF) ||  // 基本汉字
                (c >= 0x3400 && c <= 0x4DBF) ||  // 扩展A
                (c >= 0x20000 && c <= 0x2A6DF) || // 扩展B
                (c >= 0x2A700 && c <= 0x2B73F) || // 扩展C
                (c >= 0x2B740 && c <= 0x2B81F) || // 扩展D
                (c >= 0x2B820 && c <= 0x2CEAF) || // 扩展E
                (c >= 0x2CEB0 && c <= 0x2EBEF) || // 扩展F
                (c >= 0x30000 && c <= 0x3134F) || // 扩展G
                (c >= 0x3400 && c <= 0x4DB5) ||   // 扩展A
                (c >= 0xFF00 && c <= 0xFFEF);     // 全角字符
    }

    /**
     * 创建默认的表头自适应宽度处理器
     *
     * @return AutoHeaderWidthHandler实例
     */
    public static AutoHeaderWidthHandler create() {
        return new AutoHeaderWidthHandler();
    }

    /**
     * 创建自定义配置的表头自适应宽度处理器
     *
     * @param maxWidth 最大表头列宽
     * @param minWidth 最小表头列宽
     * @return AutoHeaderWidthHandler实例
     */
    public static AutoHeaderWidthHandler create(int maxWidth, int minWidth) {
        return new AutoHeaderWidthHandler(maxWidth, minWidth, HEADER_EXTRA_WIDTH, true, true);
    }

    /**
     * 创建只调整表头宽度的处理器
     *
     * @return AutoHeaderWidthHandler实例
     */
    public static AutoHeaderWidthHandler createHeaderOnly() {
        return new AutoHeaderWidthHandler(DEFAULT_MAX_HEADER_WIDTH, DEFAULT_MIN_HEADER_WIDTH,
                HEADER_EXTRA_WIDTH, true, true);
    }

    /**
     * 创建同时考虑数据列宽的处理器
     *
     * @return AutoHeaderWidthHandler实例
     */
    public static AutoHeaderWidthHandler createWithDataConsideration() {
        return new AutoHeaderWidthHandler(DEFAULT_MAX_HEADER_WIDTH, DEFAULT_MIN_HEADER_WIDTH,
                HEADER_EXTRA_WIDTH, true, false);
    }

    /**
     * 创建禁用中文优化的表头自适应宽度处理器
     *
     * @return AutoHeaderWidthHandler实例
     */
    public static AutoHeaderWidthHandler createWithoutChineseOptimization() {
        return new AutoHeaderWidthHandler(DEFAULT_MAX_HEADER_WIDTH, DEFAULT_MIN_HEADER_WIDTH,
                HEADER_EXTRA_WIDTH, false, true);
    }
}
