package com.lanshan.base.commonservice.common.excel.util;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Excel表头顺序控制工具类
 * 
 * 提供不使用 index 属性的表头顺序控制方法
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/7/6
 */
@Slf4j
public class ExcelHeaderOrderUtil {

    /**
     * 获取类的所有Excel字段信息（按声明顺序）
     * 
     * @param clazz 目标类
     * @return 字段信息列表
     */
    public static List<ExcelFieldInfo> getExcelFields(Class<?> clazz) {
        List<ExcelFieldInfo> fieldInfos = new ArrayList<>();
        
        // 获取所有字段（包括父类字段）
        List<Field> allFields = getAllFields(clazz);
        
        for (Field field : allFields) {
            ExcelProperty excelProperty = field.getAnnotation(ExcelProperty.class);
            if (excelProperty != null) {
                ExcelFieldInfo fieldInfo = new ExcelFieldInfo();
                fieldInfo.setFieldName(field.getName());
                fieldInfo.setHeaderName(getHeaderName(excelProperty));
                fieldInfo.setField(field);
                fieldInfos.add(fieldInfo);
            }
        }
        
        return fieldInfos;
    }

    /**
     * 获取所有字段（包括父类字段），按继承层次排序
     * 
     * @param clazz 目标类
     * @return 字段列表
     */
    private static List<Field> getAllFields(Class<?> clazz) {
        List<Field> allFields = new ArrayList<>();
        
        // 收集继承链上的所有类
        List<Class<?>> classHierarchy = new ArrayList<>();
        Class<?> currentClass = clazz;
        while (currentClass != null && currentClass != Object.class) {
            classHierarchy.add(currentClass);
            currentClass = currentClass.getSuperclass();
        }
        
        // 从父类到子类的顺序添加字段
        Collections.reverse(classHierarchy);
        for (Class<?> cls : classHierarchy) {
            Field[] fields = cls.getDeclaredFields();
            allFields.addAll(Arrays.asList(fields));
        }
        
        return allFields;
    }

    /**
     * 从ExcelProperty注解获取表头名称
     * 
     * @param excelProperty ExcelProperty注解
     * @return 表头名称
     */
    private static String getHeaderName(ExcelProperty excelProperty) {
        String[] value = excelProperty.value();
        if (value.length > 0 && !value[0].isEmpty()) {
            return value[0];
        }
        return "";
    }

    /**
     * 验证表头顺序是否正确
     * 
     * @param clazz 目标类
     * @param expectedOrder 期望的表头顺序
     * @return 是否匹配
     */
    public static boolean validateHeaderOrder(Class<?> clazz, List<String> expectedOrder) {
        List<ExcelFieldInfo> fieldInfos = getExcelFields(clazz);
        List<String> actualOrder = fieldInfos.stream()
                .map(ExcelFieldInfo::getHeaderName)
                .collect(Collectors.toList());
        
        boolean matches = actualOrder.equals(expectedOrder);
        
        if (!matches) {
            log.warn("表头顺序不匹配:");
            log.warn("期望顺序: {}", expectedOrder);
            log.warn("实际顺序: {}", actualOrder);
        }
        
        return matches;
    }

    /**
     * 打印类的表头信息（用于调试）
     * 
     * @param clazz 目标类
     */
    public static void printHeaderInfo(Class<?> clazz) {
        List<ExcelFieldInfo> fieldInfos = getExcelFields(clazz);
        
        System.out.println("=== " + clazz.getSimpleName() + " 表头信息 ===");
        for (int i = 0; i < fieldInfos.size(); i++) {
            ExcelFieldInfo info = fieldInfos.get(i);
            System.out.printf("%d. 字段: %-20s 表头: %s%n", 
                    i, info.getFieldName(), info.getHeaderName());
        }
        System.out.println();
    }

    /**
     * 创建表头顺序映射
     * 
     * @param clazz 目标类
     * @return 字段名到表头名的映射
     */
    public static Map<String, String> createHeaderMapping(Class<?> clazz) {
        List<ExcelFieldInfo> fieldInfos = getExcelFields(clazz);
        return fieldInfos.stream()
                .collect(Collectors.toMap(
                        ExcelFieldInfo::getFieldName,
                        ExcelFieldInfo::getHeaderName,
                        (existing, replacement) -> existing,
                        LinkedHashMap::new
                ));
    }

    /**
     * Excel字段信息
     */
    public static class ExcelFieldInfo {
        private String fieldName;
        private String headerName;
        private Field field;

        public String getFieldName() {
            return fieldName;
        }

        public void setFieldName(String fieldName) {
            this.fieldName = fieldName;
        }

        public String getHeaderName() {
            return headerName;
        }

        public void setHeaderName(String headerName) {
            this.headerName = headerName;
        }

        public Field getField() {
            return field;
        }

        public void setField(Field field) {
            this.field = field;
        }

        @Override
        public String toString() {
            return String.format("ExcelFieldInfo{fieldName='%s', headerName='%s'}", 
                    fieldName, headerName);
        }
    }

    /**
     * 表头顺序验证器
     */
    public static class HeaderOrderValidator {
        private final List<String> expectedOrder;

        public HeaderOrderValidator(List<String> expectedOrder) {
            this.expectedOrder = new ArrayList<>(expectedOrder);
        }

        public HeaderOrderValidator(String... expectedHeaders) {
            this.expectedOrder = Arrays.asList(expectedHeaders);
        }

        public boolean validate(Class<?> clazz) {
            return validateHeaderOrder(clazz, expectedOrder);
        }

        public List<String> getExpectedOrder() {
            return new ArrayList<>(expectedOrder);
        }
    }

    /**
     * 使用示例
     */
    public static void main(String[] args) {
        // 示例：验证NewStudentExportDTO的表头顺序
        List<String> expectedOrder = Arrays.asList(
                "姓名", "性别", "生源地", "学工号", "准考证号",
                "证件类型", "证件号码", "手机号", "学院编码", "学院名称",
                "专业编码", "专业名称", "班级编码", "班级名称", "楼栋编码",
                "楼栋名称", "房间号", "床位号", "学生类型", "报道状态",
                "错误信息"
        );

        // 创建验证器
        HeaderOrderValidator validator = new HeaderOrderValidator(expectedOrder);
        
        // 这里可以验证实际的类
        // boolean isValid = validator.validate(NewStudentExportDTO.class);
        // System.out.println("表头顺序验证结果: " + isValid);
    }
}
