package com.lanshan.base.commonservice.common.excel.demo;

import com.alibaba.excel.EasyExcel;
import com.lanshan.base.commonservice.common.excel.handler.AutoSizeHandler;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.List;

/**
 * 自适应大小功能演示
 * 
 * 展示如何同时实现表头宽度自适应和数据宽高自适应
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/7/6
 */
public class AutoSizeDemo {

    /**
     * 方法一：使用默认配置（推荐）
     * 
     * 自动处理：
     * - 表头宽度自适应
     * - 数据列宽自适应  
     * - 数据行高自适应
     */
    public static <T> void exportWithDefaultAutoSize(HttpServletResponse response, 
                                                    String fileName, String sheetName,
                                                    Class<T> clazz, List<T> data) throws IOException {
        // 设置响应头
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        String encodedFileName = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + encodedFileName + ".xlsx");

        // 使用默认的自适应处理器
        EasyExcel.write(response.getOutputStream(), clazz)
                .registerWriteHandler(new AutoSizeHandler())
                .sheet(sheetName)
                .doWrite(data);
    }

    /**
     * 方法二：自定义配置
     * 
     * 可以精确控制各种参数
     */
    public static <T> void exportWithCustomAutoSize(HttpServletResponse response, 
                                                   String fileName, String sheetName,
                                                   Class<T> clazz, List<T> data) throws IOException {
        // 设置响应头
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        String encodedFileName = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + encodedFileName + ".xlsx");

        // 自定义列宽配置
        AutoSizeHandler.ColumnWidthConfig columnConfig = new AutoSizeHandler.ColumnWidthConfig(
                60,    // 最大列宽（字符数）
                8,     // 最小列宽（字符数）
                true   // 启用中文字符优化
        );

        // 自定义行高配置
        AutoSizeHandler.RowHeightConfig rowConfig = new AutoSizeHandler.RowHeightConfig(
                100f,  // 最大行高（磅）
                12f,   // 最小行高（磅）
                25f,   // 表头行高（磅）
                18f,   // 默认行高（磅）
                15f,   // 每行文字高度（磅）
                true   // 启用自动换行
        );

        // 创建自定义配置的处理器
        AutoSizeHandler handler = new AutoSizeHandler(columnConfig, rowConfig);

        EasyExcel.write(response.getOutputStream(), clazz)
                .registerWriteHandler(handler)
                .sheet(sheetName)
                .doWrite(data);
    }

    /**
     * 方法三：只处理列宽（包括表头和数据）
     */
    public static <T> void exportWithColumnWidthOnly(HttpServletResponse response, 
                                                    String fileName, String sheetName,
                                                    Class<T> clazz, List<T> data) throws IOException {
        // 设置响应头
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        String encodedFileName = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + encodedFileName + ".xlsx");

        // 只配置列宽，行高使用默认值
        AutoSizeHandler.ColumnWidthConfig columnConfig = new AutoSizeHandler.ColumnWidthConfig(
                50,    // 最大列宽
                10,    // 最小列宽
                true   // 启用中文优化
        );

        // 行高配置为默认（不自适应）
        AutoSizeHandler.RowHeightConfig rowConfig = new AutoSizeHandler.RowHeightConfig();

        AutoSizeHandler handler = new AutoSizeHandler(columnConfig, rowConfig);

        EasyExcel.write(response.getOutputStream(), clazz)
                .registerWriteHandler(handler)
                .sheet(sheetName)
                .doWrite(data);
    }

    /**
     * 方法四：只处理行高
     */
    public static <T> void exportWithRowHeightOnly(HttpServletResponse response, 
                                                  String fileName, String sheetName,
                                                  Class<T> clazz, List<T> data) throws IOException {
        // 设置响应头
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        String encodedFileName = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + encodedFileName + ".xlsx");

        // 列宽配置为默认（不自适应）
        AutoSizeHandler.ColumnWidthConfig columnConfig = new AutoSizeHandler.ColumnWidthConfig();

        // 只配置行高
        AutoSizeHandler.RowHeightConfig rowConfig = new AutoSizeHandler.RowHeightConfig(
                80f,   // 最大行高
                15f,   // 最小行高
                30f,   // 表头行高
                20f,   // 默认行高
                16f,   // 每行文字高度
                true   // 启用自动换行
        );

        AutoSizeHandler handler = new AutoSizeHandler(columnConfig, rowConfig);

        EasyExcel.write(response.getOutputStream(), clazz)
                .registerWriteHandler(handler)
                .sheet(sheetName)
                .doWrite(data);
    }

    /**
     * 使用示例
     */
    public static void main(String[] args) {
        System.out.println("AutoSizeHandler 功能说明：");
        System.out.println("1. 表头宽度自适应：根据表头文字长度自动调整列宽");
        System.out.println("2. 数据列宽自适应：根据数据内容长度自动调整列宽");
        System.out.println("3. 数据行高自适应：根据数据内容和换行情况自动调整行高");
        System.out.println("4. 中文字符优化：中文字符按2个字符宽度计算");
        System.out.println("5. 最大最小限制：可设置列宽和行高的最大最小值");
        System.out.println();
        System.out.println("推荐使用方法一（默认配置），已经能满足大部分需求。");
    }
}
