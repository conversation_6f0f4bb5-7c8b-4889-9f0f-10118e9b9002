package com.lanshan.base.commonservice.common.excel.test;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.annotation.ExcelProperty;
import com.lanshan.base.commonservice.common.excel.strategy.ExcelExportStrategy;
import lombok.Data;

import java.io.FileOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * ExcelExportStrategy 测试类
 * 
 * 验证智能Handler选择功能
 * 
 * <AUTHOR>
 * @date 2025/7/6
 */
public class ExcelExportStrategyTest {

    @Data
    public static class TestData {
        @ExcelProperty("编号")
        private String id;
        
        @ExcelProperty("姓名")
        private String name;
        
        @ExcelProperty("详细地址")
        private String address;
        
        @ExcelProperty("备注信息")
        private String remark;
    }

    public static void main(String[] args) throws IOException {
        // 测试不同数据量的Handler选择
        testSmallData();
        testMediumData();
        testLargeData();
        testMassiveData();
    }

    /**
     * 测试小数据量（< 1000行）
     */
    private static void testSmallData() throws IOException {
        System.out.println("=== 测试小数据量（500行）===");
        List<TestData> dataList = createTestData(500);
        
        // 打印策略建议
        ExcelExportStrategy.printStrategyAdvice(dataList.size());
        
        // 获取Handler
        Object handler = ExcelExportStrategy.createOptimalHandler(dataList.size());
        System.out.println("选择的Handler: " + (handler != null ? handler.getClass().getSimpleName() : "无Handler"));
        
        // 导出测试
        exportToFile(dataList, "test_small_data.xlsx", handler);
        System.out.println("小数据量测试完成\n");
    }

    /**
     * 测试中等数据量（1000-10000行）
     */
    private static void testMediumData() throws IOException {
        System.out.println("=== 测试中等数据量（5000行）===");
        List<TestData> dataList = createTestData(5000);
        
        // 打印策略建议
        ExcelExportStrategy.printStrategyAdvice(dataList.size());
        
        // 获取Handler
        Object handler = ExcelExportStrategy.createOptimalHandler(dataList.size());
        System.out.println("选择的Handler: " + (handler != null ? handler.getClass().getSimpleName() : "无Handler"));
        
        // 导出测试
        exportToFile(dataList, "test_medium_data.xlsx", handler);
        System.out.println("中等数据量测试完成\n");
    }

    /**
     * 测试大数据量（10000-50000行）
     */
    private static void testLargeData() throws IOException {
        System.out.println("=== 测试大数据量（20000行）===");
        List<TestData> dataList = createTestData(20000);
        
        // 打印策略建议
        ExcelExportStrategy.printStrategyAdvice(dataList.size());
        
        // 获取Handler
        Object handler = ExcelExportStrategy.createOptimalHandler(dataList.size());
        System.out.println("选择的Handler: " + (handler != null ? handler.getClass().getSimpleName() : "无Handler"));
        
        // 导出测试
        exportToFile(dataList, "test_large_data.xlsx", handler);
        System.out.println("大数据量测试完成\n");
    }

    /**
     * 测试超大数据量（> 50000行）
     */
    private static void testMassiveData() throws IOException {
        System.out.println("=== 测试超大数据量（80000行）===");
        List<TestData> dataList = createTestData(80000);
        
        // 打印策略建议
        ExcelExportStrategy.printStrategyAdvice(dataList.size());
        
        // 获取Handler
        Object handler = ExcelExportStrategy.createOptimalHandler(dataList.size());
        System.out.println("选择的Handler: " + (handler != null ? handler.getClass().getSimpleName() : "无Handler"));
        
        // 导出测试
        exportToFile(dataList, "test_massive_data.xlsx", handler);
        System.out.println("超大数据量测试完成\n");
    }

    /**
     * 创建测试数据
     */
    private static List<TestData> createTestData(int count) {
        List<TestData> dataList = new ArrayList<>();
        
        for (int i = 1; i <= count; i++) {
            TestData data = new TestData();
            data.setId("ID" + String.format("%06d", i));
            data.setName("测试用户" + i);
            
            // 创建不同长度的地址信息
            if (i % 3 == 0) {
                data.setAddress("北京市海淀区中关村软件园信息路甲28号科实大厦B座15层1501室这是一个很长的地址信息用来测试自动换行功能");
            } else if (i % 3 == 1) {
                data.setAddress("上海市浦东新区张江高科技园区第一大道100号");
            } else {
                data.setAddress("广州市天河区");
            }
            
            // 创建不同长度的备注信息
            if (i % 4 == 0) {
                data.setRemark("这是一个很长的备注信息，包含了很多详细的说明内容，用来测试当单元格内容超过列宽时是否能够正确换行显示");
            } else if (i % 4 == 1) {
                data.setRemark("中等长度的备注信息，用来测试列宽自适应功能");
            } else if (i % 4 == 2) {
                data.setRemark("短备注");
            } else {
                data.setRemark("包含\n换行符\n的备注");
            }
            
            dataList.add(data);
        }
        
        return dataList;
    }

    /**
     * 导出到文件
     */
    private static void exportToFile(List<TestData> dataList, String fileName, Object handler) throws IOException {
        long startTime = System.currentTimeMillis();
        
        String filePath = "D:/" + fileName;
        
        var writerBuilder = EasyExcel.write(new FileOutputStream(filePath), TestData.class);
        if (handler != null) {
            writerBuilder.registerWriteHandler(handler);
        }
        
        writerBuilder.sheet("测试数据").doWrite(dataList);
        
        long endTime = System.currentTimeMillis();
        System.out.println("文件已生成: " + filePath);
        System.out.println("导出耗时: " + (endTime - startTime) + "ms");
    }
}
