package com.lanshan.base.commonservice.common.excel.test;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.annotation.ExcelProperty;
import com.lanshan.base.commonservice.common.excel.handler.HeaderWidthContentHeightHandler;
import lombok.Data;

import java.io.FileOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * HeaderWidthContentHeightHandler 测试类
 * 
 * 用于验证列宽设置和行高计算是否正确
 * 
 * <AUTHOR>
 * @date 2025/7/6
 */
public class HeaderWidthContentHeightHandlerTest {

    @Data
    public static class TestData {
        @ExcelProperty("短标题")
        private String shortHeader;
        
        @ExcelProperty("这是一个很长的表头标题用来测试列宽")
        private String longHeader;
        
        @ExcelProperty("姓名")
        private String name;
        
        @ExcelProperty("详细地址信息")
        private String address;
        
        @ExcelProperty("备注说明")
        private String remark;
    }

    public static void main(String[] args) throws IOException {
        // 创建测试数据
        List<TestData> dataList = createTestData();
        
        // 测试文件路径
        String fileName = "D:/test_header_width_content_height.xlsx";
        
        // 使用 HeaderWidthContentHeightHandler 导出
        EasyExcel.write(new FileOutputStream(fileName), TestData.class)
                .registerWriteHandler(new HeaderWidthContentHeightHandler())
                .sheet("测试数据")
                .doWrite(dataList);
        
        System.out.println("测试文件已生成: " + fileName);
        System.out.println("请检查以下内容:");
        System.out.println("1. 列宽是否根据表头和内容智能调整");
        System.out.println("2. 长内容是否正确换行显示");
        System.out.println("3. 表头是否正确显示且居中加粗");
        System.out.println("4. 行高是否根据内容自动调整");
    }

    private static List<TestData> createTestData() {
        List<TestData> dataList = new ArrayList<>();
        
        // 测试数据1：短内容
        TestData data1 = new TestData();
        data1.setShortHeader("短");
        data1.setLongHeader("短内容");
        data1.setName("张三");
        data1.setAddress("北京市");
        data1.setRemark("无");
        dataList.add(data1);
        
        // 测试数据2：长内容
        TestData data2 = new TestData();
        data2.setShortHeader("这是一个很长的内容，用来测试当内容比表头长时的列宽调整效果");
        data2.setLongHeader("正常长度内容");
        data2.setName("李四");
        data2.setAddress("北京市海淀区中关村软件园信息路甲28号科实大厦B座15层1501室这是一个很长的地址信息用来测试自动换行功能");
        data2.setRemark("这是一个很长的备注信息，包含了很多详细的说明内容，用来测试当单元格内容超过列宽时是否能够正确换行显示，并且行高是否能够自动调整以完整显示所有内容");
        dataList.add(data2);
        
        // 测试数据3：包含换行符的内容
        TestData data3 = new TestData();
        data3.setShortHeader("包含\n换行符\n的内容");
        data3.setLongHeader("多行\n内容\n测试");
        data3.setName("王五");
        data3.setAddress("上海市浦东新区\n张江高科技园区\n第一大道100号");
        data3.setRemark("第一行备注\n第二行备注\n第三行备注");
        dataList.add(data3);
        
        // 测试数据4：中英文混合
        TestData data4 = new TestData();
        data4.setShortHeader("Mixed中英文Content");
        data4.setLongHeader("English and 中文 Mixed Content Test");
        data4.setName("John Smith 约翰·史密斯");
        data4.setAddress("Room 1501, Building B, Keshi Building, No.28 Xinxi Road, Zhongguancun Software Park, Haidian District, Beijing 北京市海淀区中关村软件园信息路甲28号科实大厦B座1501室");
        data4.setRemark("This is a mixed language remark 这是一个中英文混合的备注信息，用来测试中文字符宽度优化功能是否正常工作");
        dataList.add(data4);
        
        // 测试数据5：数字和特殊字符
        TestData data5 = new TestData();
        data5.setShortHeader("123456789");
        data5.setLongHeader("Number: 1234567890");
        data5.setName("测试用户001");
        data5.setAddress("地址编号：100001，详细地址：某某省某某市某某区某某街道某某号某某小区某某栋某某单元某某室");
        data5.setRemark("特殊字符测试：!@#$%^&*()_+-=[]{}|;':\",./<>? 以及一些数字：1234567890");
        dataList.add(data5);
        
        return dataList;
    }
}
